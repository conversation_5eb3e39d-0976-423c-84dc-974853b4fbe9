# LtAI 异构资源管理平台

基于 React + Node.js 开发的AI模型训练管理平台，提供端到端的 AI MLOps 流程管理。

## 功能特性

### 核心功能
- 🔐 **用户认证和权限管理** - 多租户管理、角色权限控制
- 💻 **开发环境管理** - 支持多种AI框架的开发环境创建和管理
- 🚀 **训练任务管理** - 分布式训练、任务监控、参数管理
- 💾 **存储管理** - 数据存储、文件管理、共享功能
- 🤖 **模型管理** - 模型版本控制、可视化展示
- 🌐 **模型服务** - 在线部署、推理服务、API调用
- 🐳 **镜像仓库** - Docker镜像管理、版本控制
- 📊 **资源监控** - GPU/CPU/内存实时监控
- 🏷️ **数据标注** - 在线图像标注工具

### 技术架构
- **前端**: React 18 + TypeScript + Vite + Ant Design
- **后端**: Node.js + Express + TypeScript + MongoDB
- **状态管理**: Zustand
- **API通信**: Axios + React Query
- **认证**: JWT
- **日志**: Winston

## 快速开始

### 环境要求
- Node.js >= 16.0.0
- MongoDB >= 4.4
- npm 或 yarn

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd ltai
```

2. **安装依赖**
```bash
# 安装前端依赖
cd frontend
npm install

# 安装后端依赖
cd ../backend
npm install
```

3. **配置环境变量**
```bash
# 后端配置
cd backend
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息

# 前端配置
cd ../frontend
cp .env.example .env
# 编辑 .env 文件，配置API地址
```

4. **启动数据库**
```bash
# 确保 MongoDB 服务正在运行
mongod
```

5. **初始化数据**
```bash
cd backend
npm run init-admin
```

6. **启动服务**
```bash
# 启动后端服务
cd backend
npm run dev

# 启动前端服务（新终端）
cd frontend
npm run dev
```

7. **访问应用**
- 前端地址: http://localhost:3000
- 后端API: http://localhost:5000
- 默认管理员账号: <EMAIL> / 123456

## 项目结构

```
ltai/
├── frontend/                 # 前端项目
│   ├── src/
│   │   ├── components/      # 通用组件
│   │   ├── pages/          # 页面组件
│   │   ├── services/       # API服务
│   │   ├── store/          # 状态管理
│   │   ├── types/          # 类型定义
│   │   └── utils/          # 工具函数
│   ├── package.json
│   └── vite.config.ts
├── backend/                  # 后端项目
│   ├── src/
│   │   ├── controllers/    # 控制器
│   │   ├── models/         # 数据模型
│   │   ├── routes/         # 路由
│   │   ├── middleware/     # 中间件
│   │   ├── services/       # 业务服务
│   │   ├── utils/          # 工具函数
│   │   └── config/         # 配置文件
│   ├── package.json
│   └── tsconfig.json
└── README.md
```

## 开发指南

### 前端开发
```bash
cd frontend
npm run dev      # 启动开发服务器
npm run build    # 构建生产版本
npm run preview  # 预览生产版本
```

### 后端开发
```bash
cd backend
npm run dev      # 启动开发服务器
npm run build    # 编译TypeScript
npm start        # 启动生产服务器
```

### 数据库管理
```bash
cd backend
npm run init-admin    # 初始化管理员用户
```

## API文档

### 认证相关
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `GET /api/auth/me` - 获取当前用户信息

### 用户管理
- `GET /api/users` - 获取用户列表
- `GET /api/users/:id` - 获取用户详情
- `PUT /api/users/:id` - 更新用户信息
- `DELETE /api/users/:id` - 删除用户

### 开发环境
- `GET /api/environments` - 获取环境列表
- `POST /api/environments` - 创建环境
- `GET /api/environments/:id` - 获取环境详情
- `POST /api/environments/:id/start` - 启动环境
- `POST /api/environments/:id/stop` - 停止环境
- `DELETE /api/environments/:id` - 删除环境

## 部署说明

### Docker 部署
```bash
# 构建镜像
docker build -t ltai-frontend ./frontend
docker build -t ltai-backend ./backend

# 运行容器
docker-compose up -d
```

### 生产环境配置
1. 配置反向代理（Nginx）
2. 设置HTTPS证书
3. 配置数据库集群
4. 设置监控和日志收集

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目地址: [GitHub Repository]
- 问题反馈: [Issues]
- 邮箱: <EMAIL>

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现基础的用户认证和权限管理
- 完成开发环境管理功能
- 添加基础的资源监控功能
