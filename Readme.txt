LtAI异构资源管理平台
软件介绍
 

      LtAI 异构资源管理平台依托强大的研发能力以及丰富的行业经验，针对机器/深度学习科研、实训教学等场景提供一站式解决方案。LtAI 打造出了一款功能完善的人工智能开发平台，平台能提供端到端的 AI MLOps 流程管理，涵盖数据标注、算法开发、模型训练、模型管理、模型服务等 AI 全生命周期流程的支持。

 

 



 

 

      LtAI 异构资源管理平台采用容器轻量级虚拟化方式作为基础，实现对多集群多节点的 GPU、CPU、内存、存储等基础设施资源的池化；基于 Kubernetes 定制化开发的编排调度工具，实现资源高效灵活调度；同时 LtAI 企业级的设计使平台具备多租户多层级的用户管理、权限管理、资源管理、GPU 共享等丰富的平台能力，全方位满足用户对 AI 开发平台高可用、高可靠、高稳定的要求。
      平台为用户提供简洁的 WEB 界面，丰富的功能以及多样化的工具。如开发模块提供一键式环境生成、在线交互式开发工具 Mlab ；模型训练提供参数调优、分布式并行训练等；模型服务提供在线的模型部署推理、模型服务调用；平台同时提供在线的数据标注工具等，实现一站式 AI 开发。
      深度学习框架镜像以插件的方式接入系统，集成了多种业界常用框架，如 Tensorflow, Caffe, PyTorch  和 MXnet 等，支持自定义扩展，极大提升整体系统的扩展性和可维护性

      

 

平台功能架构
 

 

      LtAI 异构资源管理平台分为算力层（基础设施资源）、资源调度、平台功能层，整体架构如下图：

 

 



 

 

      基础设施层主要包括物理机、虚拟机、存储设备、网络设备、一体机等 IAAS 层资源，和一体机等多种模式组成。
      资源调度层借助 Docker 引擎实现 CPU、GPU、内存、存储等资源的轻量级虚拟化，基于Kubernetes 定制化研发实现对任务和资源的灵活编排调度，具有多租户隔离，任务资源逻辑隔离特点，结合高可靠的存储服务、分布式并行训练服务等功能组件，为上层功能模块提供坚实的基石。
      平台功能层提供端到端的 AI 科研流程支撑，其中用户端包含了开发模块、AI 框架、训练模块、数据管理模块、模型服务模块、镜像仓库、工单管理等；管理端包含计量计费、多租户管理、告警监控设置、平台运营运维等。

 
 

产品管理
 

 

      产品管理提供模型训练开发、模型训练、存储管理、模型服务、镜像仓库等用户创建的环境统一管理功能；
      开发环境：对平台全部用户创建的环境进行统一管理，包括不限于环境信息查看、环境回收、环境恢复、环境删除等操作。
      模型训练：对平台全部用户创建的训练任务进行统一管理，包括不限于训练任务信息查看、任务查看、日志查看等操作。
      存储管理：对平台全部用户创建的存储空间进行统一管理，包括不限于用户存储空间信息查看、存储扩容、文件操作等功能。
      模型服务：对平台全部用户创建的模型服务进行统一管理，包括不限于用户模型服务信息查看、启动/停止服务、更新记录、日志查看等。
      镜像仓库：对平台全部用户创建的镜像文件进行统一管理，包括不限于用户镜像信息查看、修改、删除等操作。

 
 

开发环境
 

 

      开发环境用于用户在机器/深度学习中对算法和模型的开发，用户按需申请所需的资源，快速方便的创建一个开发环境，同时摒弃对 IAAS 层设备的感知能力。

 

 



 

 

LtAI 异构资源管理平台开发环境提供的功能：


      · AI 框架，集成多种 AI 框架供用户选择，如 TensorFlow、PyTorch、Keras 等，
      · 自定义扩展，支持自定义 AI 框架；· 多种算力规格资源选择（ GPU、CPU、内存、硬盘等）；
      · 资源监控，GPU、CPU、内存的实时使用情况，显卡使用率、风扇使用率、以及温度监控等信息；
      · 环境访问，环境提供多种访问方式。如 SSH 访问（可通过 SSH 工具登录至环境中训练使用），MLAB (在线交互开发) 方式（通过 Jupyter Lab 在线交互式笔记训练使用）；
      · 环境变更，对开发环境进行在线调整，如调整计算资源、开发框架等；

 
 

训练任务
 

 

      训练任务支持单机单卡、单机多卡、多机多卡等方式，提升模型训练速度。主要具备如下能力：

 

 



 

 

      · 支持单节点训练和分布式并行训练；
      · 任务监控，训练任务 GPU、CPU、内存的实时使用情况，显卡使用率、风扇使用率、以及温度监控等信息；
      · 多版本管理，训练任务可以保存多个版本，方便后续调用；
      · 参数保存，对于设定的模型参数可以进行保存，后续基于参数可直接创建训练；
      · 训练日志，查看阶段下每个任务的训练日志；
      · 结果通知，训练完成通过短信、邮箱等方式将结果通知用户。

 

 
存储管理
 

 

      存储空间是承载用户个人数据以及持久化保存数据的基础，平台上每个用户都可以按需去申请自己的存储空间。
      存储服务功能点：

      · 通过 SCP 或 Web 方式访问管理存储数据；
      · 用户对自己的存储空间拥有上传/下载数据的权限；
      · 通过存储可以管理训练中使用的数据和程序；
      · 把存储共享给其他用户使用；

 
 

数据标注
 

 

      在 AI 科研中，对数据的标注工作也是一件重要的事情。平台将标注工具服务化，集成一套多人同时在线的图像标注工具，可以在线直接对图片进行标注，标注后的数据可用于开发环境或训练任务，同时平台也支持标注工具的扩展。

 
 

模型管理
 

 

      模型管理针对用户在平台训练完成的模型或者第三方上传的模型进行可视化的多版本统一管理。
      · 平台训练任务完成的模型一键式管理，包含模型的版本、框架、模型精度参数等；
      · 上传的模型，通过选择模型文件、框架参数等进行管理。

 

 

模型服务
 

 

      模型服务用于把用户开发模型在线发布成服务，进行在线推理和预测：
      · 模型发布，提供在线发能力，支持多实例同时发布；
      · 模型服务，提供在线上传推理文件进行推理验证，提供接口、地址、token 等远程服务调用；
      · 模型实例：启动/停止/删除服务，支持设定服务运行时长，实时日志记录显示等。

 

 



 
 

镜像仓库
 

 

      平台提供高度可扩展的高性能镜像管理服务，主要功能如下：
      · 用户拥有自己的个人私有镜像仓库；
      · 镜像管理，镜像的上传下载、多版本管理等；
      · 镜像共享，具备共享能力，方便其他用户使用。

 
 

平台方案优势
 

 

· 节约成本
 
 

      资源弹性伸缩
 

 

      LtAI 平台环境下，所有资源都集中在数据中心，可实现资源的集中管控，弹性调度。管理员可实时对服务器和用户业务环境中 GPU、CPU、磁盘等各类资源使用率等指标进行数据查看与统计，分析目前物理机、用户环境资源的状况，进而实时调整资源分配。

 
 

      提升资源利用率
 

 

      资源的集中管理与分配，提高了资源利用率。传统AI科研方式的服务器或工作站的 CPU/GPU 平均利用率不足 30%，在 LtAI 异构资源管理平台平台环境下，云数据中心的 CPU/GPU 利用率可控制在 75% 左右，整体资源利用率提升。

 
 

· 极致性能
 

 

      借助容器、K8S 等云计算技术打造的 LtAI 平台具备并行、高吞吐、低时延的极致性能，结合超强算力的 GPU 集群的支撑，相较于传统 IT 架构，计算性能提升 10 倍以上。
      用户在平台上可以进行秒级的业务创建，实验 AI 环境的快速部署；同时借助于分布式训练模式，实现单机多卡、多机多卡的高效训练，大幅度提升模型训练能力，充分释放计算集群性能。

 
 

· 灵活部署
 

 

      LtAI 平台提供灵活的部署方式，包括私有本地部署、公有云形式部署等。同时 LtAI 异构资源管理平台平台对硬件要求极低，在通用的 X86 服务器或者一些国产 ARM 服务器如华为鲲鹏、飞腾等也可以实现部署。

 

 



 
 

· 模块丰富
 

 

      LtAI 平台提供丰富的功能模块，全方位为用户提供 AI 科研服务。整体涵盖 AI 开发流程所需的功能，主要功能模块如下：

 

 



 

 

      除此之外，各个系统中又有众多小功能，最大化为用户提供便捷。如在 AI 开发环境中创建快照、保存为镜像模板共享给他人，在模型训练提供多版本、多参数、基于参数生成训练等。

 
 

· 可扩展性
 

 

      系统秉承开放、中立的技术原则，对产品和服务不做任何绑定。平台在设计之初就保持可扩展能力，如镜像自定义扩展、提供标准 API 接口等，能够最大程度上满足用户的各种需求。
      同时，LtAI 平台会长期对市面主流的软硬件产品进行兼容性和功能性测试，提升产品的通用能力。
