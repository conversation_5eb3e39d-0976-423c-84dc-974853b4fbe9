# 服务器配置
PORT=5000
NODE_ENV=development

# 前端URL
FRONTEND_URL=http://localhost:3000

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/ltai

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRE=7d

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=100MB

# Redis配置（可选，用于缓存和会话）
REDIS_URL=redis://localhost:6379

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password

# 短信配置
SMS_API_KEY=your-sms-api-key
SMS_API_SECRET=your-sms-api-secret

# 日志级别
LOG_LEVEL=info

# Kubernetes配置
KUBE_CONFIG_PATH=/path/to/kubeconfig

# Docker配置
DOCKER_HOST=unix:///var/run/docker.sock

# GPU监控配置
NVIDIA_SMI_PATH=/usr/bin/nvidia-smi
