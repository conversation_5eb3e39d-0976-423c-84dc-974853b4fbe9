import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';
import crypto from 'crypto';
import { Storage } from '../models/Storage';
import { authenticate, authorize } from '../middleware/auth';
import logger from '../utils/logger';

const router = express.Router();

// 配置文件上传
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const user = (req as any).user;
    const uploadPath = path.join(process.cwd(), 'uploads', user.tenant, user._id.toString());

    try {
      await fs.mkdir(uploadPath, { recursive: true });
      cb(null, uploadPath);
    } catch (error) {
      cb(error as Error, '');
    }
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 100 * 1024 * 1024 // 100MB
  },
  fileFilter: (req, file, cb) => {
    // 允许所有文件类型
    cb(null, true);
  }
});

// 获取存储列表
router.get('/', authenticate, async (req, res) => {
  try {
    const user = (req as any).user;
    const { page = 1, pageSize = 10, type } = req.query;
    const skip = (Number(page) - 1) * Number(pageSize);

    let query: any = { userId: user._id };

    if (type) query.type = type;

    const storages = await Storage.find(query)
      .populate('userId', 'username email')
      .populate('sharing.permissions.userId', 'username email')
      .skip(skip)
      .limit(Number(pageSize))
      .sort({ createdAt: -1 });

    const total = await Storage.countDocuments(query);

    res.json({
      success: true,
      message: '获取存储列表成功',
      code: 200,
      data: {
        storages,
        pagination: {
          page: Number(page),
          pageSize: Number(pageSize),
          total,
          totalPages: Math.ceil(total / Number(pageSize))
        }
      }
    });
  } catch (error) {
    logger.error('Get storages error:', error);
    res.status(500).json({
      success: false,
      message: '获取存储列表失败',
      code: 500
    });
  }
});

// 创建存储空间
router.post('/', authenticate, async (req, res) => {
  try {
    const user = (req as any).user;
    const { name, description, type, quota } = req.body;

    // 生成存储路径
    const storagePath = path.join('storage', user.tenant, user._id.toString(), name);

    const storageData = await Storage.create({
      name,
      description,
      type: type || 'personal',
      path: storagePath,
      quota,
      userId: user._id,
      tenant: user.tenant,
      files: [],
      sharing: {
        enabled: false,
        permissions: [],
        publicAccess: false
      },
      backup: {
        enabled: false,
        frequency: 'weekly',
        retention: 30
      },
      sync: {
        enabled: false,
        syncStatus: 'idle'
      }
    });

    // 创建物理目录
    const fullPath = path.join(process.cwd(), storagePath);
    await fs.mkdir(fullPath, { recursive: true });

    logger.info(`Storage created: ${storageData.name} by ${user.email}`);

    res.status(201).json({
      success: true,
      message: '创建存储空间成功',
      code: 201,
      data: { storage: storageData }
    });
  } catch (error) {
    logger.error('Create storage error:', error);
    res.status(500).json({
      success: false,
      message: '创建存储空间失败',
      code: 500
    });
  }
});

// 获取单个存储详情
router.get('/:id', authenticate, async (req, res) => {
  try {
    const user = (req as any).user;
    const storage = await Storage.findOne({
      _id: req.params.id,
      userId: user._id
    }).populate('userId', 'username email')
      .populate('sharing.permissions.userId', 'username email');

    if (!storage) {
      return res.status(404).json({
        success: false,
        message: '存储空间不存在',
        code: 404
      });
    }

    res.json({
      success: true,
      message: '获取存储详情成功',
      code: 200,
      data: { storage }
    });
  } catch (error) {
    logger.error('Get storage error:', error);
    res.status(500).json({
      success: false,
      message: '获取存储详情失败',
      code: 500
    });
  }
});

// 上传文件
router.post('/:id/upload', authenticate, upload.array('files', 10), async (req, res) => {
  try {
    const user = (req as any).user;
    const files = req.files as Express.Multer.File[];

    if (!files || files.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有上传文件',
        code: 400
      });
    }

    const storage = await Storage.findOne({
      _id: req.params.id,
      userId: user._id
    });

    if (!storage) {
      return res.status(404).json({
        success: false,
        message: '存储空间不存在',
        code: 404
      });
    }

    const uploadedFiles = [];

    for (const file of files) {
      // 计算文件校验和
      const fileBuffer = await fs.readFile(file.path);
      const checksum = crypto.createHash('md5').update(fileBuffer).digest('hex');

      const fileInfo = {
        name: file.originalname,
        path: file.path,
        size: file.size,
        type: path.extname(file.originalname),
        mimeType: file.mimetype,
        uploadedAt: new Date(),
        lastModified: new Date(),
        checksum,
        isDirectory: false,
        parentPath: req.body.parentPath || '/'
      };

      storage.files.push(fileInfo);
      uploadedFiles.push(fileInfo);
    }

    await storage.save();

    logger.info(`Files uploaded to storage: ${storage.name} by ${user.email}`);

    res.json({
      success: true,
      message: '文件上传成功',
      code: 200,
      data: { files: uploadedFiles }
    });
  } catch (error) {
    logger.error('Upload files error:', error);
    res.status(500).json({
      success: false,
      message: '文件上传失败',
      code: 500
    });
  }
});

// 删除文件
router.delete('/:id/files/:fileName', authenticate, async (req, res) => {
  try {
    const user = (req as any).user;
    const { fileName } = req.params;

    const storage = await Storage.findOne({
      _id: req.params.id,
      userId: user._id
    });

    if (!storage) {
      return res.status(404).json({
        success: false,
        message: '存储空间不存在',
        code: 404
      });
    }

    const fileIndex = storage.files.findIndex(file => file.name === fileName);

    if (fileIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '文件不存在',
        code: 404
      });
    }

    const file = storage.files[fileIndex];

    // 删除物理文件
    try {
      await fs.unlink(file.path);
    } catch (error) {
      logger.warn(`Failed to delete physical file: ${file.path}`);
    }

    // 从数据库中删除文件记录
    storage.files.splice(fileIndex, 1);
    await storage.save();

    logger.info(`File deleted from storage: ${fileName} by ${user.email}`);

    res.json({
      success: true,
      message: '文件删除成功',
      code: 200
    });
  } catch (error) {
    logger.error('Delete file error:', error);
    res.status(500).json({
      success: false,
      message: '文件删除失败',
      code: 500
    });
  }
});

// 删除存储空间
router.delete('/:id', authenticate, async (req, res) => {
  try {
    const user = (req as any).user;
    const storage = await Storage.findOneAndDelete({
      _id: req.params.id,
      userId: user._id
    });

    if (!storage) {
      return res.status(404).json({
        success: false,
        message: '存储空间不存在',
        code: 404
      });
    }

    // 删除物理目录
    const fullPath = path.join(process.cwd(), (storage as any).path);
    try {
      await fs.rmdir(fullPath, { recursive: true });
    } catch (error) {
      logger.warn(`Failed to delete physical directory: ${fullPath}`);
    }

    logger.info(`Storage deleted: ${(storage as any).name} by ${user.email}`);

    res.json({
      success: true,
      message: '存储空间删除成功',
      code: 200
    });
  } catch (error) {
    logger.error('Delete storage error:', error);
    res.status(500).json({
      success: false,
      message: '删除存储空间失败',
      code: 500
    });
  }
});

export default router;
