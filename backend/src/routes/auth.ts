import express from 'express';
import jwt from 'jsonwebtoken';
import { User } from '../models/User';
import { authenticate } from '../middleware/auth';
import logger from '../utils/logger';

const router = express.Router();

// 生成JWT令牌
const generateToken = (id: string): string => {
  const secret = process.env.JWT_SECRET || 'fallback-secret';
  const payload = { id };
  // 使用any类型绕过TypeScript类型检查问题
  const options: any = { expiresIn: process.env.JWT_EXPIRE || '7d' };
  return jwt.sign(payload, secret, options) as string;
};

// 用户注册
router.post('/register', async (req, res) => {
  try {
    const { username, email, password, role, tenant } = req.body;

    // 检查用户是否已存在
    const existingUser = await User.findOne({
      $or: [{ email }, { username }]
    });

    if (existingUser) {
      res.status(400).json({
        success: false,
        message: '用户名或邮箱已存在',
        code: 400
      });
      return;
    }

    // 创建新用户
    const user = await User.create({
      username,
      email,
      password,
      role: role || 'user',
      tenant: tenant || 'default'
    });

    // 生成令牌
    const token = generateToken(user._id.toString());

    logger.info(`New user registered: ${user.email}`);

    res.status(201).json({
      success: true,
      message: '用户注册成功',
      code: 201,
      data: {
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          role: user.role,
          tenant: user.tenant
        },
        token
      }
    });
  } catch (error) {
    logger.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: '注册失败',
      code: 500
    });
  }
});

// 用户登录
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // 验证输入
    if (!email || !password) {
      res.status(400).json({
        success: false,
        message: '请提供邮箱和密码',
        code: 400
      });
      return;
    }

    // 查找用户并包含密码字段
    const user = await User.findOne({ email }).select('+password');

    if (!user || !(await user.comparePassword(password))) {
      res.status(401).json({
        success: false,
        message: '邮箱或密码错误',
        code: 401
      });
      return;
    }

    if (!user.isActive) {
      res.status(401).json({
        success: false,
        message: '账户已被禁用',
        code: 401
      });
      return;
    }

    // 更新最后登录时间
    user.lastLogin = new Date();
    await user.save();

    // 生成令牌
    const token = generateToken(user._id.toString());

    logger.info(`User logged in: ${user.email}`);

    res.json({
      success: true,
      message: '登录成功',
      code: 200,
      data: {
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          role: user.role,
          tenant: user.tenant
        },
        token
      }
    });
  } catch (error) {
    logger.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: '登录失败',
      code: 500
    });
  }
});

// 获取当前用户信息
router.get('/me', authenticate, async (req, res) => {
  try {
    const user = await User.findById((req as any).user.id);
    
    if (!user) {
      res.status(404).json({
        success: false,
        message: '用户不存在',
        code: 404
      });
      return;
    }

    res.json({
      success: true,
      message: '获取用户信息成功',
      code: 200,
      data: {
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          role: user.role,
          tenant: user.tenant,
          isActive: user.isActive,
          lastLogin: user.lastLogin,
          createdAt: user.createdAt
        }
      }
    });
  } catch (error) {
    logger.error('Get user info error:', error);
    res.status(500).json({
      success: false,
      message: '获取用户信息失败',
      code: 500
    });
  }
});

export default router;
