import express from 'express';
import { User } from '../models/User';
import { authenticate, authorize } from '../middleware/auth';
import logger from '../utils/logger';

const router = express.Router();

// 获取所有用户（仅管理员）
router.get('/', authenticate, authorize('admin'), async (req, res) => {
  try {
    const { page = 1, pageSize = 10, search } = req.query;
    const skip = (Number(page) - 1) * Number(pageSize);

    let query: any = {};
    if (search) {
      query = {
        $or: [
          { username: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } }
        ]
      };
    }

    const users = await User.find(query)
      .select('-password')
      .skip(skip)
      .limit(Number(pageSize))
      .sort({ createdAt: -1 });

    const total = await User.countDocuments(query);

    res.json({
      success: true,
      message: '获取用户列表成功',
      code: 200,
      data: {
        users,
        pagination: {
          page: Number(page),
          pageSize: Number(pageSize),
          total,
          totalPages: Math.ceil(total / Number(pageSize))
        }
      }
    });
  } catch (error) {
    logger.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: '获取用户列表失败',
      code: 500
    });
  }
});

// 获取单个用户
router.get('/:id', authenticate, async (req, res): Promise<void> => {
  try {
    const user = await User.findById(req.params.id).select('-password');
    
    if (!user) {
      res.status(404).json({
        success: false,
        message: '用户不存在',
        code: 404
      });
      return;
    }

    res.json({
      success: true,
      message: '获取用户信息成功',
      code: 200,
      data: { user }
    });
  } catch (error) {
    logger.error('Get user error:', error);
    res.status(500).json({
      success: false,
      message: '获取用户信息失败',
      code: 500
    });
    return;
  }
});

// 更新用户信息
router.put('/:id', authenticate, async (req, res): Promise<void> => {
  try {
    const currentUser = (req as any).user;
    const { id } = req.params;
    const { username, email, role, isActive } = req.body;

    // 检查权限：只有管理员或用户本人可以更新
    if (currentUser.role !== 'admin' && currentUser._id.toString() !== id) {
      res.status(403).json({
        success: false,
        message: '权限不足',
        code: 403
      });
      return;
    }

    const updateData: any = {};
    if (username) updateData.username = username;
    if (email) updateData.email = email;
    
    // 只有管理员可以更新角色和状态
    if (currentUser.role === 'admin') {
      if (role) updateData.role = role;
      if (typeof isActive === 'boolean') updateData.isActive = isActive;
    }

    const user = await User.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).select('-password');

    if (!user) {
      res.status(404).json({
        success: false,
        message: '用户不存在',
        code: 404
      });
      return;
    }

    res.json({
      success: true,
      message: '用户信息更新成功',
      code: 200,
      data: { user }
    });
  } catch (error) {
    logger.error('Update user error:', error);
    res.status(500).json({
      success: false,
      message: '用户信息更新失败',
      code: 500
    });
    return;
  }
});

// 删除用户（仅管理员）
router.delete('/:id', authenticate, authorize('admin'), async (req, res): Promise<void> => {
  try {
    const user = await User.findByIdAndDelete(req.params.id);
    
    if (!user) {
      res.status(404).json({
        success: false,
        message: '用户不存在',
        code: 404
      });
      return;
    }

    res.json({
      success: true,
      message: '用户删除成功',
      code: 200
    });
  } catch (error) {
    logger.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      message: '用户删除失败',
      code: 500
    });
    return;
  }
});

export default router;
