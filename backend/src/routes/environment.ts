import express from 'express';
import { authenticate } from '../middleware/auth';

const router = express.Router();

// 获取开发环境列表
router.get('/', authenticate, async (req, res) => {
  res.json({
    success: true,
    message: '获取开发环境列表成功',
    code: 200,
    data: { environments: [] }
  });
});

// 创建开发环境
router.post('/', authenticate, async (req, res) => {
  res.json({
    success: true,
    message: '创建开发环境成功',
    code: 201,
    data: { environment: {} }
  });
});

export default router;
