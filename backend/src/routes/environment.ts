import express from 'express';
import { Environment } from '../models/Environment';
import { authenticate, authorize } from '../middleware/auth';
import logger from '../utils/logger';

const router = express.Router();

// 获取开发环境列表
router.get('/', authenticate, async (req, res) => {
  try {
    const user = (req as any).user;
    const { page = 1, pageSize = 10, status, framework } = req.query;
    const skip = (Number(page) - 1) * Number(pageSize);

    let query: any = { userId: user._id };

    if (status) query.status = status;
    if (framework) query.framework = framework;

    const environments = await Environment.find(query)
      .populate('userId', 'username email')
      .skip(skip)
      .limit(Number(pageSize))
      .sort({ createdAt: -1 });

    const total = await Environment.countDocuments(query);

    res.json({
      success: true,
      message: '获取开发环境列表成功',
      code: 200,
      data: {
        environments,
        pagination: {
          page: Number(page),
          pageSize: Number(pageSize),
          total,
          totalPages: Math.ceil(total / Number(pageSize))
        }
      }
    });
  } catch (error) {
    logger.error('Get environments error:', error);
    res.status(500).json({
      success: false,
      message: '获取开发环境列表失败',
      code: 500
    });
  }
});

// 创建开发环境
router.post('/', authenticate, async (req, res) => {
  try {
    const user = (req as any).user;
    const {
      name,
      description,
      framework,
      version,
      resources,
      accessMethods,
      autoStop
    } = req.body;

    // 生成随机端口
    const generatePort = () => Math.floor(Math.random() * (65535 - 30000) + 30000);

    const environment = await Environment.create({
      name,
      description,
      framework,
      version,
      resources,
      accessMethods: accessMethods || ['ssh', 'jupyter'],
      ports: {
        ssh: generatePort(),
        jupyter: generatePort(),
        tensorboard: accessMethods?.includes('tensorboard') ? generatePort() : undefined
      },
      userId: user._id,
      tenant: user.tenant,
      autoStop
    });

    logger.info(`Environment created: ${environment.name} by ${user.email}`);

    res.status(201).json({
      success: true,
      message: '创建开发环境成功',
      code: 201,
      data: { environment }
    });
  } catch (error) {
    logger.error('Create environment error:', error);
    res.status(500).json({
      success: false,
      message: '创建开发环境失败',
      code: 500
    });
  }
});

// 获取单个环境详情
router.get('/:id', authenticate, async (req, res) => {
  try {
    const user = (req as any).user;
    const environment = await Environment.findOne({
      _id: req.params.id,
      userId: user._id
    }).populate('userId', 'username email');

    if (!environment) {
      return res.status(404).json({
        success: false,
        message: '环境不存在',
        code: 404
      });
    }

    res.json({
      success: true,
      message: '获取环境详情成功',
      code: 200,
      data: { environment }
    });
  } catch (error) {
    logger.error('Get environment error:', error);
    res.status(500).json({
      success: false,
      message: '获取环境详情失败',
      code: 500
    });
  }
});

// 启动环境
router.post('/:id/start', authenticate, async (req, res) => {
  try {
    const user = (req as any).user;
    const environment = await Environment.findOneAndUpdate(
      { _id: req.params.id, userId: user._id },
      { status: 'running', lastAccessedAt: new Date() },
      { new: true }
    );

    if (!environment) {
      return res.status(404).json({
        success: false,
        message: '环境不存在',
        code: 404
      });
    }

    logger.info(`Environment started: ${environment.name} by ${user.email}`);

    res.json({
      success: true,
      message: '环境启动成功',
      code: 200,
      data: { environment }
    });
  } catch (error) {
    logger.error('Start environment error:', error);
    res.status(500).json({
      success: false,
      message: '启动环境失败',
      code: 500
    });
  }
});

// 停止环境
router.post('/:id/stop', authenticate, async (req, res) => {
  try {
    const user = (req as any).user;
    const environment = await Environment.findOneAndUpdate(
      { _id: req.params.id, userId: user._id },
      { status: 'stopped' },
      { new: true }
    );

    if (!environment) {
      return res.status(404).json({
        success: false,
        message: '环境不存在',
        code: 404
      });
    }

    logger.info(`Environment stopped: ${environment.name} by ${user.email}`);

    res.json({
      success: true,
      message: '环境停止成功',
      code: 200,
      data: { environment }
    });
  } catch (error) {
    logger.error('Stop environment error:', error);
    res.status(500).json({
      success: false,
      message: '停止环境失败',
      code: 500
    });
  }
});

// 删除环境
router.delete('/:id', authenticate, async (req, res) => {
  try {
    const user = (req as any).user;
    const environment = await Environment.findOneAndDelete({
      _id: req.params.id,
      userId: user._id
    });

    if (!environment) {
      return res.status(404).json({
        success: false,
        message: '环境不存在',
        code: 404
      });
    }

    logger.info(`Environment deleted: ${environment.name} by ${user.email}`);

    res.json({
      success: true,
      message: '环境删除成功',
      code: 200
    });
  } catch (error) {
    logger.error('Delete environment error:', error);
    res.status(500).json({
      success: false,
      message: '删除环境失败',
      code: 500
    });
  }
});

export default router;
