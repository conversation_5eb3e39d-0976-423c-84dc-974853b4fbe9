import express from 'express';
import { ResourceMonitor, IResourceMonitor } from '../models/ResourceMonitor';
import { authenticate } from '../middleware/auth';
import logger from '../utils/logger';

const router = express.Router();

// 获取资源监控数据列表
router.get('/', authenticate, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      nodeId,
      nodeType,
      nodeStatus,
      startTime,
      endTime,
      sortBy = 'timestamp',
      sortOrder = 'desc'
    } = req.query;

    const filter: any = { tenant: req.user.tenant };

    // 添加筛选条件
    if (nodeId) {
      filter.nodeId = nodeId;
    }
    if (nodeType) {
      filter.nodeType = nodeType;
    }
    if (nodeStatus) {
      filter.nodeStatus = nodeStatus;
    }
    if (startTime || endTime) {
      filter.timestamp = {};
      if (startTime) {
        filter.timestamp.$gte = new Date(startTime as string);
      }
      if (endTime) {
        filter.timestamp.$lte = new Date(endTime as string);
      }
    }

    const skip = (Number(page) - 1) * Number(limit);
    const sort: any = {};
    sort[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

    const [monitors, total] = await Promise.all([
      ResourceMonitor.find(filter)
        .sort(sort)
        .skip(skip)
        .limit(Number(limit))
        .lean(),
      ResourceMonitor.countDocuments(filter)
    ]);

    res.json({
      success: true,
      data: {
        monitors,
        pagination: {
          current: Number(page),
          pageSize: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      }
    });
  } catch (error: any) {
    logger.error('获取资源监控数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取资源监控数据失败',
      error: error.message
    });
  }
});

// 获取节点列表
router.get('/nodes', authenticate, async (req, res) => {
  try {
    const nodes = await ResourceMonitor.aggregate([
      { $match: { tenant: req.user.tenant } },
      {
        $group: {
          _id: '$nodeId',
          nodeName: { $last: '$nodeName' },
          nodeType: { $last: '$nodeType' },
          nodeStatus: { $last: '$nodeStatus' },
          lastUpdate: { $max: '$timestamp' },
          avgCpuUsage: { $avg: '$cpu.usage' },
          avgMemoryUsage: { $avg: { $multiply: [{ $divide: ['$memory.used', '$memory.total'] }, 100] } },
          avgGpuUsage: { $avg: '$stats.avgGpuUsage' },
          alertCount: { $sum: { $size: { $filter: { input: '$alerts', cond: { $eq: ['$$this.resolved', false] } } } } }
        }
      },
      {
        $project: {
          nodeId: '$_id',
          nodeName: 1,
          nodeType: 1,
          nodeStatus: 1,
          lastUpdate: 1,
          avgCpuUsage: { $round: ['$avgCpuUsage', 2] },
          avgMemoryUsage: { $round: ['$avgMemoryUsage', 2] },
          avgGpuUsage: { $round: ['$avgGpuUsage', 2] },
          alertCount: 1,
          _id: 0
        }
      },
      { $sort: { lastUpdate: -1 } }
    ]);

    res.json({
      success: true,
      data: { nodes }
    });
  } catch (error: any) {
    logger.error('获取节点列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取节点列表失败',
      error: error.message
    });
  }
});

// 获取特定节点的详细监控数据
router.get('/nodes/:nodeId', authenticate, async (req, res) => {
  try {
    const { nodeId } = req.params;
    const {
      startTime,
      endTime,
      limit = 100
    } = req.query;

    const filter: any = {
      tenant: req.user.tenant,
      nodeId
    };

    if (startTime || endTime) {
      filter.timestamp = {};
      if (startTime) {
        filter.timestamp.$gte = new Date(startTime as string);
      }
      if (endTime) {
        filter.timestamp.$lte = new Date(endTime as string);
      }
    }

    const monitors = await ResourceMonitor.find(filter)
      .sort({ timestamp: -1 })
      .limit(Number(limit))
      .lean();

    if (monitors.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到该节点的监控数据'
      });
    }

    // 获取最新的监控数据作为当前状态
    const currentStatus = monitors[0];

    // 计算统计数据
    const stats = {
      avgCpuUsage: monitors.reduce((sum, m) => sum + m.cpu.usage, 0) / monitors.length,
      avgMemoryUsage: monitors.reduce((sum, m) => sum + (m.memory.used / m.memory.total * 100), 0) / monitors.length,
      avgGpuUsage: monitors.reduce((sum, m) => sum + m.stats.avgGpuUsage, 0) / monitors.length,
      maxCpuUsage: Math.max(...monitors.map(m => m.cpu.usage)),
      maxMemoryUsage: Math.max(...monitors.map(m => m.memory.used / m.memory.total * 100)),
      maxGpuUsage: Math.max(...monitors.map(m => m.stats.avgGpuUsage))
    };

    res.json({
      success: true,
      data: {
        currentStatus,
        history: monitors,
        stats: {
          avgCpuUsage: Math.round(stats.avgCpuUsage * 100) / 100,
          avgMemoryUsage: Math.round(stats.avgMemoryUsage * 100) / 100,
          avgGpuUsage: Math.round(stats.avgGpuUsage * 100) / 100,
          maxCpuUsage: Math.round(stats.maxCpuUsage * 100) / 100,
          maxMemoryUsage: Math.round(stats.maxMemoryUsage * 100) / 100,
          maxGpuUsage: Math.round(stats.maxGpuUsage * 100) / 100
        }
      }
    });
  } catch (error: any) {
    logger.error('获取节点监控数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取节点监控数据失败',
      error: error.message
    });
  }
});

// 获取集群总览统计
router.get('/overview', authenticate, async (req, res) => {
  try {
    const { timeRange = '1h' } = req.query;

    // 计算时间范围
    const now = new Date();
    let startTime: Date;

    switch (timeRange) {
      case '1h':
        startTime = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case '6h':
        startTime = new Date(now.getTime() - 6 * 60 * 60 * 1000);
        break;
      case '24h':
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      default:
        startTime = new Date(now.getTime() - 60 * 60 * 1000);
    }

    const overview = await ResourceMonitor.aggregate([
      {
        $match: {
          tenant: req.user.tenant,
          timestamp: { $gte: startTime }
        }
      },
      {
        $group: {
          _id: '$nodeId',
          nodeName: { $last: '$nodeName' },
          nodeType: { $last: '$nodeType' },
          nodeStatus: { $last: '$nodeStatus' },
          totalCores: { $last: '$cpu.cores' },
          totalMemory: { $last: '$memory.total' },
          totalGpus: { $last: '$gpu.count' },
          avgCpuUsage: { $avg: '$cpu.usage' },
          avgMemoryUsage: { $avg: { $multiply: [{ $divide: ['$memory.used', '$memory.total'] }, 100] } },
          avgGpuUsage: { $avg: '$stats.avgGpuUsage' },
          activeAlerts: { $sum: { $size: { $filter: { input: '$alerts', cond: { $eq: ['$$this.resolved', false] } } } } }
        }
      },
      {
        $group: {
          _id: null,
          totalNodes: { $sum: 1 },
          onlineNodes: { $sum: { $cond: [{ $eq: ['$nodeStatus', 'online'] }, 1, 0] } },
          offlineNodes: { $sum: { $cond: [{ $eq: ['$nodeStatus', 'offline'] }, 1, 0] } },
          totalCores: { $sum: '$totalCores' },
          totalMemory: { $sum: '$totalMemory' },
          totalGpus: { $sum: '$totalGpus' },
          avgCpuUsage: { $avg: '$avgCpuUsage' },
          avgMemoryUsage: { $avg: '$avgMemoryUsage' },
          avgGpuUsage: { $avg: '$avgGpuUsage' },
          totalAlerts: { $sum: '$activeAlerts' }
        }
      }
    ]);

    const result = overview[0] || {
      totalNodes: 0,
      onlineNodes: 0,
      offlineNodes: 0,
      totalCores: 0,
      totalMemory: 0,
      totalGpus: 0,
      avgCpuUsage: 0,
      avgMemoryUsage: 0,
      avgGpuUsage: 0,
      totalAlerts: 0
    };

    // 格式化结果
    const formattedResult = {
      ...result,
      avgCpuUsage: Math.round((result.avgCpuUsage || 0) * 100) / 100,
      avgMemoryUsage: Math.round((result.avgMemoryUsage || 0) * 100) / 100,
      avgGpuUsage: Math.round((result.avgGpuUsage || 0) * 100) / 100,
      totalMemoryGB: Math.round((result.totalMemory || 0) / 1024 * 100) / 100
    };

    res.json({
      success: true,
      data: { overview: formattedResult }
    });
  } catch (error: any) {
    logger.error('获取集群总览失败:', error);
    res.status(500).json({
      success: false,
      message: '获取集群总览失败',
      error: error.message
    });
  }
});

// 获取告警列表
router.get('/alerts', authenticate, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      level,
      type,
      resolved,
      nodeId
    } = req.query;

    const matchStage: any = { tenant: req.user.tenant };

    if (nodeId) {
      matchStage.nodeId = nodeId;
    }

    const alertFilter: any = {};
    if (level) {
      alertFilter['alerts.level'] = level;
    }
    if (type) {
      alertFilter['alerts.type'] = type;
    }
    if (resolved !== undefined) {
      alertFilter['alerts.resolved'] = resolved === 'true';
    }

    const pipeline = [
      { $match: matchStage },
      { $unwind: '$alerts' },
      { $match: alertFilter },
      {
        $project: {
          nodeId: 1,
          nodeName: 1,
          alert: '$alerts',
          timestamp: '$timestamp'
        }
      },
      { $sort: { 'alert.timestamp': -1 } },
      { $skip: (Number(page) - 1) * Number(limit) },
      { $limit: Number(limit) }
    ];

    const countPipeline = [
      { $match: matchStage },
      { $unwind: '$alerts' },
      { $match: alertFilter },
      { $count: 'total' }
    ];

    const [alerts, countResult] = await Promise.all([
      ResourceMonitor.aggregate(pipeline),
      ResourceMonitor.aggregate(countPipeline)
    ]);

    const total = countResult[0]?.total || 0;

    res.json({
      success: true,
      data: {
        alerts,
        pagination: {
          current: Number(page),
          pageSize: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      }
    });
  } catch (error: any) {
    logger.error('获取告警列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取告警列表失败',
      error: error.message
    });
  }
});

// 解决告警
router.put('/alerts/:nodeId/:alertIndex/resolve', authenticate, async (req, res) => {
  try {
    const { nodeId, alertIndex } = req.params;

    const monitor = await ResourceMonitor.findOne({
      tenant: req.user.tenant,
      nodeId
    }).sort({ timestamp: -1 });

    if (!monitor) {
      return res.status(404).json({
        success: false,
        message: '未找到监控数据'
      });
    }

    const alertIdx = Number(alertIndex);
    if (alertIdx < 0 || alertIdx >= monitor.alerts.length) {
      return res.status(400).json({
        success: false,
        message: '无效的告警索引'
      });
    }

    monitor.alerts[alertIdx].resolved = true;
    monitor.alerts[alertIdx].resolvedAt = new Date();
    await monitor.save();

    logger.info(`告警已解决: 节点 ${nodeId}, 告警索引 ${alertIndex}`);

    res.json({
      success: true,
      message: '告警已解决'
    });
  } catch (error: any) {
    logger.error('解决告警失败:', error);
    res.status(500).json({
      success: false,
      message: '解决告警失败',
      error: error.message
    });
  }
});

// 上报监控数据（供监控代理使用）
router.post('/report', authenticate, async (req, res) => {
  try {
    const monitorData = {
      ...req.body,
      tenant: req.user.tenant,
      timestamp: new Date()
    };

    // 验证必需字段
    if (!monitorData.nodeId || !monitorData.nodeName) {
      return res.status(400).json({
        success: false,
        message: '缺少必需的节点信息'
      });
    }

    // 检查告警条件
    const alerts = [];

    // CPU告警检查
    if (monitorData.cpu && monitorData.cpu.usage > 90) {
      alerts.push({
        level: 'critical',
        type: 'cpu',
        message: `CPU使用率过高: ${monitorData.cpu.usage}%`,
        threshold: 90,
        currentValue: monitorData.cpu.usage,
        timestamp: new Date(),
        resolved: false
      });
    }

    // 内存告警检查
    if (monitorData.memory) {
      const memoryUsage = (monitorData.memory.used / monitorData.memory.total) * 100;
      if (memoryUsage > 90) {
        alerts.push({
          level: 'critical',
          type: 'memory',
          message: `内存使用率过高: ${memoryUsage.toFixed(2)}%`,
          threshold: 90,
          currentValue: memoryUsage,
          timestamp: new Date(),
          resolved: false
        });
      }
    }

    // GPU告警检查
    if (monitorData.gpu && monitorData.gpu.devices) {
      monitorData.gpu.devices.forEach((device: any, index: number) => {
        if (device.usage > 95) {
          alerts.push({
            level: 'warning',
            type: 'gpu',
            message: `GPU ${index} 使用率过高: ${device.usage}%`,
            threshold: 95,
            currentValue: device.usage,
            timestamp: new Date(),
            resolved: false
          });
        }
        if (device.temperature > 85) {
          alerts.push({
            level: 'critical',
            type: 'gpu',
            message: `GPU ${index} 温度过高: ${device.temperature}°C`,
            threshold: 85,
            currentValue: device.temperature,
            timestamp: new Date(),
            resolved: false
          });
        }
      });
    }

    // 存储告警检查
    if (monitorData.storage && monitorData.storage.devices) {
      monitorData.storage.devices.forEach((device: any) => {
        if (device.usage > 90) {
          alerts.push({
            level: 'warning',
            type: 'storage',
            message: `存储设备 ${device.device} 使用率过高: ${device.usage}%`,
            threshold: 90,
            currentValue: device.usage,
            timestamp: new Date(),
            resolved: false
          });
        }
      });
    }

    monitorData.alerts = alerts;

    // 计算统计数据
    const stats = {
      avgCpuUsage: monitorData.cpu?.usage || 0,
      avgGpuUsage: monitorData.gpu?.devices ?
        monitorData.gpu.devices.reduce((sum: number, device: any) => sum + device.usage, 0) / monitorData.gpu.devices.length : 0,
      avgMemoryUsage: monitorData.memory ?
        (monitorData.memory.used / monitorData.memory.total) * 100 : 0,
      maxCpuUsage: monitorData.cpu?.usage || 0,
      maxGpuUsage: monitorData.gpu?.devices ?
        Math.max(...monitorData.gpu.devices.map((device: any) => device.usage)) : 0,
      maxMemoryUsage: monitorData.memory ?
        (monitorData.memory.used / monitorData.memory.total) * 100 : 0,
      totalRequests: 0,
      errorCount: alerts.length
    };

    monitorData.stats = stats;

    const monitor = new ResourceMonitor(monitorData);
    await monitor.save();

    logger.info(`监控数据已保存: 节点 ${monitorData.nodeId}, 告警数量 ${alerts.length}`);

    res.json({
      success: true,
      message: '监控数据上报成功',
      data: {
        alertCount: alerts.length,
        criticalAlerts: alerts.filter(alert => alert.level === 'critical').length
      }
    });
  } catch (error: any) {
    logger.error('上报监控数据失败:', error);
    res.status(500).json({
      success: false,
      message: '上报监控数据失败',
      error: error.message
    });
  }
});

// 获取资源使用趋势
router.get('/trends', authenticate, async (req, res) => {
  try {
    const {
      nodeId,
      timeRange = '24h',
      metric = 'cpu'
    } = req.query;

    // 计算时间范围
    const now = new Date();
    let startTime: Date;
    let groupBy: string;

    switch (timeRange) {
      case '1h':
        startTime = new Date(now.getTime() - 60 * 60 * 1000);
        groupBy = '%Y-%m-%d %H:%M';
        break;
      case '6h':
        startTime = new Date(now.getTime() - 6 * 60 * 60 * 1000);
        groupBy = '%Y-%m-%d %H:%M';
        break;
      case '24h':
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        groupBy = '%Y-%m-%d %H:00';
        break;
      case '7d':
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        groupBy = '%Y-%m-%d';
        break;
      default:
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        groupBy = '%Y-%m-%d %H:00';
    }

    const matchStage: any = {
      tenant: req.user.tenant,
      timestamp: { $gte: startTime }
    };

    if (nodeId) {
      matchStage.nodeId = nodeId;
    }

    let valueField: string;
    switch (metric) {
      case 'cpu':
        valueField = '$cpu.usage';
        break;
      case 'memory':
        valueField = { $multiply: [{ $divide: ['$memory.used', '$memory.total'] }, 100] };
        break;
      case 'gpu':
        valueField = '$stats.avgGpuUsage';
        break;
      default:
        valueField = '$cpu.usage';
    }

    const trends = await ResourceMonitor.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: {
            time: { $dateToString: { format: groupBy, date: '$timestamp' } },
            nodeId: '$nodeId'
          },
          avgValue: { $avg: valueField },
          maxValue: { $max: valueField },
          minValue: { $min: valueField },
          count: { $sum: 1 }
        }
      },
      {
        $group: {
          _id: '$_id.time',
          avgValue: { $avg: '$avgValue' },
          maxValue: { $max: '$maxValue' },
          minValue: { $min: '$minValue' },
          nodeCount: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    res.json({
      success: true,
      data: {
        trends: trends.map(trend => ({
          time: trend._id,
          avgValue: Math.round(trend.avgValue * 100) / 100,
          maxValue: Math.round(trend.maxValue * 100) / 100,
          minValue: Math.round(trend.minValue * 100) / 100,
          nodeCount: trend.nodeCount
        })),
        metric,
        timeRange
      }
    });
  } catch (error: any) {
    logger.error('获取资源使用趋势失败:', error);
    res.status(500).json({
      success: false,
      message: '获取资源使用趋势失败',
      error: error.message
    });
  }
});

export default router;
