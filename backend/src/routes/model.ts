import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';
import crypto from 'crypto';
import { Model } from '../models/Model';
import { authenticate, authorize } from '../middleware/auth';
import logger from '../utils/logger';

const router = express.Router();

// 配置模型文件上传
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const user = (req as any).user;
    const uploadPath = path.join(process.cwd(), 'models', user.tenant, user._id.toString());

    try {
      await fs.mkdir(uploadPath, { recursive: true });
      cb(null, uploadPath);
    } catch (error) {
      cb(error as Error, '');
    }
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 500 * 1024 * 1024 // 500MB
  },
  fileFilter: (req, file, cb) => {
    // 允许常见的模型文件格式
    const allowedTypes = ['.h5', '.pb', '.pth', '.pt', '.onnx', '.pkl', '.joblib', '.json', '.yaml', '.yml'];
    const ext = path.extname(file.originalname).toLowerCase();

    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('不支持的文件格式'), false);
    }
  }
});

// 获取模型列表
router.get('/', authenticate, async (req, res) => {
  try {
    const user = (req as any).user;
    const { page = 1, pageSize = 10, framework, type, tags } = req.query;
    const skip = (Number(page) - 1) * Number(pageSize);

    let query: any = { userId: user._id };

    if (framework) query.framework = framework;
    if (type) query.type = type;
    if (tags) {
      const tagArray = Array.isArray(tags) ? tags : [tags];
      query.tags = { $in: tagArray };
    }

    const models = await Model.find(query)
      .populate('userId', 'username email')
      .populate('training.trainingJobId', 'name status')
      .skip(skip)
      .limit(Number(pageSize))
      .sort({ createdAt: -1 });

    const total = await Model.countDocuments(query);

    res.json({
      success: true,
      message: '获取模型列表成功',
      code: 200,
      data: {
        models,
        pagination: {
          page: Number(page),
          pageSize: Number(pageSize),
          total,
          totalPages: Math.ceil(total / Number(pageSize))
        }
      }
    });
  } catch (error) {
    logger.error('Get models error:', error);
    res.status(500).json({
      success: false,
      message: '获取模型列表失败',
      code: 500
    });
  }
});

// 上传模型
router.post('/', authenticate, upload.fields([
  { name: 'modelFile', maxCount: 1 },
  { name: 'weightsFile', maxCount: 1 },
  { name: 'configFile', maxCount: 1 },
  { name: 'metadataFile', maxCount: 1 }
]), async (req, res) => {
  try {
    const user = (req as any).user;
    const files = req.files as { [fieldname: string]: Express.Multer.File[] };

    if (!files.modelFile || files.modelFile.length === 0) {
      return res.status(400).json({
        success: false,
        message: '模型文件是必需的',
        code: 400
      });
    }

    const modelFile = files.modelFile[0];

    // 计算文件校验和
    const fileBuffer = await fs.readFile(modelFile.path);
    const checksum = crypto.createHash('md5').update(fileBuffer).digest('hex');

    // 计算总文件大小
    let totalSize = modelFile.size;
    const fileInfo: any = {
      modelFile: modelFile.path,
      size: totalSize,
      checksum
    };

    if (files.weightsFile && files.weightsFile.length > 0) {
      fileInfo.weightsFile = files.weightsFile[0].path;
      totalSize += files.weightsFile[0].size;
    }

    if (files.configFile && files.configFile.length > 0) {
      fileInfo.configFile = files.configFile[0].path;
      totalSize += files.configFile[0].size;
    }

    if (files.metadataFile && files.metadataFile.length > 0) {
      fileInfo.metadataFile = files.metadataFile[0].path;
      totalSize += files.metadataFile[0].size;
    }

    fileInfo.size = totalSize;

    const modelData = await Model.create({
      ...req.body,
      files: fileInfo,
      userId: user._id,
      tenant: user.tenant,
      stats: {
        downloadCount: 0,
        viewCount: 0,
        deploymentCount: 0
      }
    });

    logger.info(`Model uploaded: ${modelData.name} by ${user.email}`);

    res.status(201).json({
      success: true,
      message: '模型上传成功',
      code: 201,
      data: { model: modelData }
    });
  } catch (error) {
    logger.error('Upload model error:', error);
    res.status(500).json({
      success: false,
      message: '模型上传失败',
      code: 500
    });
  }
});

// 获取单个模型详情
router.get('/:id', authenticate, async (req, res) => {
  try {
    const user = (req as any).user;
    const model = await Model.findOne({
      _id: req.params.id,
      userId: user._id
    }).populate('userId', 'username email')
      .populate('training.trainingJobId', 'name status')
      .populate('versioning.parentModelId', 'name version');

    if (!model) {
      return res.status(404).json({
        success: false,
        message: '模型不存在',
        code: 404
      });
    }

    // 增加查看次数
    model.stats.viewCount += 1;
    model.stats.lastAccessedAt = new Date();
    await model.save();

    res.json({
      success: true,
      message: '获取模型详情成功',
      code: 200,
      data: { model }
    });
  } catch (error) {
    logger.error('Get model error:', error);
    res.status(500).json({
      success: false,
      message: '获取模型详情失败',
      code: 500
    });
  }
});

// 下载模型
router.get('/:id/download', authenticate, async (req, res) => {
  try {
    const user = (req as any).user;
    const model = await Model.findOne({
      _id: req.params.id,
      userId: user._id
    });

    if (!model) {
      return res.status(404).json({
        success: false,
        message: '模型不存在',
        code: 404
      });
    }

    const filePath = model.files.modelFile;

    // 检查文件是否存在
    try {
      await fs.access(filePath);
    } catch (error) {
      return res.status(404).json({
        success: false,
        message: '模型文件不存在',
        code: 404
      });
    }

    // 增加下载次数
    model.stats.downloadCount += 1;
    await model.save();

    // 设置下载响应头
    res.setHeader('Content-Disposition', `attachment; filename="${model.name}-${model.version}${path.extname(filePath)}"`);
    res.setHeader('Content-Type', 'application/octet-stream');

    // 发送文件
    res.sendFile(path.resolve(filePath));

    logger.info(`Model downloaded: ${model.name} by ${user.email}`);
  } catch (error) {
    logger.error('Download model error:', error);
    res.status(500).json({
      success: false,
      message: '模型下载失败',
      code: 500
    });
  }
});

// 删除模型
router.delete('/:id', authenticate, async (req, res) => {
  try {
    const user = (req as any).user;
    const model = await Model.findOneAndDelete({
      _id: req.params.id,
      userId: user._id
    });

    if (!model) {
      return res.status(404).json({
        success: false,
        message: '模型不存在',
        code: 404
      });
    }

    // 删除物理文件
    const filesToDelete = [
      model.files.modelFile,
      model.files.weightsFile,
      model.files.configFile,
      model.files.metadataFile
    ].filter(Boolean);

    for (const filePath of filesToDelete) {
      try {
        await fs.unlink(filePath as string);
      } catch (error) {
        logger.warn(`Failed to delete model file: ${filePath}`);
      }
    }

    logger.info(`Model deleted: ${model.name} by ${user.email}`);

    res.json({
      success: true,
      message: '模型删除成功',
      code: 200
    });
  } catch (error) {
    logger.error('Delete model error:', error);
    res.status(500).json({
      success: false,
      message: '删除模型失败',
      code: 500
    });
  }
});

export default router;
