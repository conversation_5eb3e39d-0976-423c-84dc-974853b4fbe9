import express from 'express';
import { authenticate } from '../middleware/auth';

const router = express.Router();

// 获取训练任务列表
router.get('/', authenticate, async (req, res) => {
  res.json({
    success: true,
    message: '获取训练任务列表成功',
    code: 200,
    data: { jobs: [] }
  });
});

// 创建训练任务
router.post('/', authenticate, async (req, res) => {
  res.json({
    success: true,
    message: '创建训练任务成功',
    code: 201,
    data: { job: {} }
  });
});

export default router;
