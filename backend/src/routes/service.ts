import express from 'express';
import { ModelService } from '../models/ModelService';
import { Model } from '../models/Model';
import { authenticate, authorize } from '../middleware/auth';
import logger from '../utils/logger';

const router = express.Router();

// 获取服务列表
router.get('/', authenticate, async (req, res) => {
  try {
    const user = (req as any).user;
    const { page = 1, pageSize = 10, environment, status } = req.query;
    const skip = (Number(page) - 1) * Number(pageSize);

    let query: any = { userId: user._id };

    if (environment) query['deployment.environment'] = environment;
    if (status) query['status.state'] = status;

    const services = await ModelService.find(query)
      .populate('userId', 'username email')
      .populate('modelId', 'name version framework')
      .skip(skip)
      .limit(Number(pageSize))
      .sort({ createdAt: -1 });

    const total = await ModelService.countDocuments(query);

    res.json({
      success: true,
      message: '获取服务列表成功',
      code: 200,
      data: {
        services,
        pagination: {
          page: Number(page),
          pageSize: Number(pageSize),
          total,
          totalPages: Math.ceil(total / Number(pageSize))
        }
      }
    });
  } catch (error) {
    logger.error('Get services error:', error);
    res.status(500).json({
      success: false,
      message: '获取服务列表失败',
      code: 500
    });
  }
});

// 创建服务
router.post('/', authenticate, async (req, res) => {
  try {
    const user = (req as any).user;

    // 验证模型是否存在
    const model = await Model.findOne({
      _id: req.body.modelId,
      userId: user._id
    });

    if (!model) {
      return res.status(404).json({
        success: false,
        message: '模型不存在',
        code: 404
      });
    }

    // 生成服务端点
    const endpoint = `/api/inference/${req.body.name.toLowerCase().replace(/\s+/g, '-')}`;

    const serviceData = await ModelService.create({
      ...req.body,
      config: {
        ...req.body.config,
        endpoint
      },
      userId: user._id,
      tenant: user.tenant,
      status: {
        state: 'pending',
        health: 'unknown',
        uptime: 0
      },
      stats: {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        averageResponseTime: 0,
        dailyStats: []
      }
    });

    logger.info(`Model service created: ${serviceData.name} by ${user.email}`);

    res.status(201).json({
      success: true,
      message: '服务创建成功',
      code: 201,
      data: { service: serviceData }
    });
  } catch (error) {
    logger.error('Create service error:', error);
    res.status(500).json({
      success: false,
      message: '创建服务失败',
      code: 500
    });
  }
});

// 获取单个服务详情
router.get('/:id', authenticate, async (req, res) => {
  try {
    const user = (req as any).user;
    const service = await ModelService.findOne({
      _id: req.params.id,
      userId: user._id
    }).populate('userId', 'username email')
      .populate('modelId', 'name version framework type');

    if (!service) {
      return res.status(404).json({
        success: false,
        message: '服务不存在',
        code: 404
      });
    }

    res.json({
      success: true,
      message: '获取服务详情成功',
      code: 200,
      data: { service }
    });
  } catch (error) {
    logger.error('Get service error:', error);
    res.status(500).json({
      success: false,
      message: '获取服务详情失败',
      code: 500
    });
  }
});

// 启动服务
router.post('/:id/start', authenticate, async (req, res) => {
  try {
    const user = (req as any).user;
    const service = await ModelService.findOne({
      _id: req.params.id,
      userId: user._id
    });

    if (!service) {
      return res.status(404).json({
        success: false,
        message: '服务不存在',
        code: 404
      });
    }

    if (service.status.state === 'running') {
      return res.status(400).json({
        success: false,
        message: '服务已在运行中',
        code: 400
      });
    }

    // 更新服务状态
    service.status.state = 'deploying';
    service.status.startedAt = new Date();
    await service.save();

    // 这里应该调用实际的部署逻辑
    // 模拟部署过程
    setTimeout(async () => {
      try {
        service.status.state = 'running';
        service.status.health = 'healthy';
        service.status.lastHealthCheck = new Date();
        await service.save();
      } catch (error) {
        logger.error('Service deployment error:', error);
        service.status.state = 'error';
        service.status.errorMessage = '部署失败';
        await service.save();
      }
    }, 5000);

    logger.info(`Service started: ${service.name} by ${user.email}`);

    res.json({
      success: true,
      message: '服务启动中',
      code: 200,
      data: { service }
    });
  } catch (error) {
    logger.error('Start service error:', error);
    res.status(500).json({
      success: false,
      message: '启动服务失败',
      code: 500
    });
  }
});

// 停止服务
router.post('/:id/stop', authenticate, async (req, res) => {
  try {
    const user = (req as any).user;
    const service = await ModelService.findOne({
      _id: req.params.id,
      userId: user._id
    });

    if (!service) {
      return res.status(404).json({
        success: false,
        message: '服务不存在',
        code: 404
      });
    }

    if (service.status.state === 'stopped') {
      return res.status(400).json({
        success: false,
        message: '服务已停止',
        code: 400
      });
    }

    // 更新服务状态
    service.status.state = 'stopped';
    service.status.stoppedAt = new Date();
    service.status.health = 'unknown';

    // 计算运行时间
    if (service.status.startedAt) {
      const uptime = Date.now() - service.status.startedAt.getTime();
      service.status.uptime += uptime;
    }

    await service.save();

    logger.info(`Service stopped: ${service.name} by ${user.email}`);

    res.json({
      success: true,
      message: '服务已停止',
      code: 200,
      data: { service }
    });
  } catch (error) {
    logger.error('Stop service error:', error);
    res.status(500).json({
      success: false,
      message: '停止服务失败',
      code: 500
    });
  }
});

// 删除服务
router.delete('/:id', authenticate, async (req, res) => {
  try {
    const user = (req as any).user;
    const service = await ModelService.findOneAndDelete({
      _id: req.params.id,
      userId: user._id
    });

    if (!service) {
      return res.status(404).json({
        success: false,
        message: '服务不存在',
        code: 404
      });
    }

    logger.info(`Service deleted: ${service.name} by ${user.email}`);

    res.json({
      success: true,
      message: '服务删除成功',
      code: 200
    });
  } catch (error) {
    logger.error('Delete service error:', error);
    res.status(500).json({
      success: false,
      message: '删除服务失败',
      code: 500
    });
  }
});

// 获取服务监控数据
router.get('/:id/metrics', authenticate, async (req, res) => {
  try {
    const user = (req as any).user;
    const service = await ModelService.findOne({
      _id: req.params.id,
      userId: user._id
    });

    if (!service) {
      return res.status(404).json({
        success: false,
        message: '服务不存在',
        code: 404
      });
    }

    res.json({
      success: true,
      message: '获取监控数据成功',
      code: 200,
      data: {
        metrics: service.monitoring.metrics,
        stats: service.stats,
        dailyStats: service.stats.dailyStats.slice(-30) // 最近30天
      }
    });
  } catch (error) {
    logger.error('Get service metrics error:', error);
    res.status(500).json({
      success: false,
      message: '获取监控数据失败',
      code: 500
    });
  }
});

// 模型推理接口
router.post('/:id/predict', authenticate, async (req, res) => {
  try {
    const user = (req as any).user;
    const service = await ModelService.findOne({
      _id: req.params.id,
      userId: user._id
    }).populate('modelId');

    if (!service) {
      return res.status(404).json({
        success: false,
        message: '服务不存在',
        code: 404
      });
    }

    if (service.status.state !== 'running') {
      return res.status(400).json({
        success: false,
        message: '服务未运行',
        code: 400
      });
    }

    const startTime = Date.now();

    // 这里应该调用实际的模型推理逻辑
    // 模拟推理过程
    const mockPrediction = {
      result: 'prediction_result',
      confidence: 0.95,
      timestamp: new Date().toISOString()
    };

    const responseTime = Date.now() - startTime;

    // 更新统计信息
    service.stats.totalRequests += 1;
    service.stats.successfulRequests += 1;
    service.stats.averageResponseTime =
      (service.stats.averageResponseTime * (service.stats.totalRequests - 1) + responseTime) / service.stats.totalRequests;
    service.stats.lastRequestAt = new Date();

    // 更新监控指标
    service.monitoring.metrics.requestCount += 1;
    service.monitoring.metrics.averageLatency = service.stats.averageResponseTime;

    await service.save();

    res.json({
      success: true,
      message: '推理成功',
      code: 200,
      data: {
        prediction: mockPrediction,
        responseTime
      }
    });
  } catch (error) {
    logger.error('Model prediction error:', error);

    // 更新错误统计
    try {
      const service = await ModelService.findById(req.params.id);
      if (service) {
        service.stats.totalRequests += 1;
        service.stats.failedRequests += 1;
        service.monitoring.metrics.errorCount += 1;
        await service.save();
      }
    } catch (updateError) {
      logger.error('Update error stats failed:', updateError);
    }

    res.status(500).json({
      success: false,
      message: '推理失败',
      code: 500
    });
  }
});

export default router;
