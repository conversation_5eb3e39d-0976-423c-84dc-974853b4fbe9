import mongoose from 'mongoose';
import { User } from '../models/User';
import logger from '../utils/logger';
import dotenv from 'dotenv';

dotenv.config();

const initAdmin = async () => {
  try {
    // 连接数据库
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/ltai';
    await mongoose.connect(mongoURI);
    logger.info('Connected to MongoDB');

    // 检查是否已存在管理员用户
    const existingAdmin = await User.findOne({ role: 'admin' });
    
    if (existingAdmin) {
      logger.info('Admin user already exists');
      process.exit(0);
    }

    // 创建默认管理员用户
    const adminUser = await User.create({
      username: 'admin',
      email: '<EMAIL>',
      password: '123456',
      role: 'admin',
      tenant: 'default'
    });

    logger.info(`Admin user created successfully: ${adminUser.email}`);
    logger.info('Default login credentials:');
    logger.info('Email: <EMAIL>');
    logger.info('Password: 123456');

    process.exit(0);
  } catch (error) {
    logger.error('Error creating admin user:', error);
    process.exit(1);
  }
};

initAdmin();
