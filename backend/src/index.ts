import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import { createServer } from 'http';
import { Server } from 'socket.io';
import connectDB from './config/database';
import logger from './utils/logger';

// 路由导入
import authRoutes from './routes/auth';
import userRoutes from './routes/user';
import environmentRoutes from './routes/environment';
import trainingRoutes from './routes/training';
import storageRoutes from './routes/storage';
import modelRoutes from './routes/model';
import serviceRoutes from './routes/service';
import imageRoutes from './routes/image';
import monitorRoutes from './routes/monitor';

// 中间件导入
import { errorHandler } from './middleware/errorHandler';
import { notFound } from './middleware/notFound';

dotenv.config();

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 5000;

// 连接数据库
connectDB();

// 中间件
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || "http://localhost:3000",
  credentials: true
}));
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }));
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// 路由
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/environments', environmentRoutes);
app.use('/api/training', trainingRoutes);
app.use('/api/storage', storageRoutes);
app.use('/api/models', modelRoutes);
app.use('/api/services', serviceRoutes);
app.use('/api/images', imageRoutes);
app.use('/api/monitor', monitorRoutes);

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// WebSocket连接处理
io.on('connection', (socket) => {
  logger.info(`Client connected: ${socket.id}`);
  
  socket.on('disconnect', () => {
    logger.info(`Client disconnected: ${socket.id}`);
  });
});

// 错误处理中间件
app.use(notFound);
app.use(errorHandler);

server.listen(PORT, () => {
  logger.info(`Server running on port ${PORT}`);
});

export { io };
