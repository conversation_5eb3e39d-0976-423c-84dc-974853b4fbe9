{"name": "@antv/g6", "version": "5.0.49", "description": "A Graph Visualization Framework in JavaScript", "keywords": ["antv", "g6", "graph", "graph analysis", "graph editor", "graph visualization", "relational data"], "homepage": "https://g6.antv.antgroup.com/", "bugs": {"url": "https://github.com/antvis/g6/issues"}, "repository": {"type": "git", "url": "https://github.com/antvis/g6"}, "license": "MIT", "author": "https://github.com/orgs/antvis/people", "main": "lib/index.js", "module": "esm/index.js", "types": "lib/index.d.ts", "files": ["src", "esm", "lib", "dist", "README"], "dependencies": {"@antv/algorithm": "^0.1.26", "@antv/component": "^2.1.3", "@antv/event-emitter": "^0.1.3", "@antv/g": "^6.1.24", "@antv/g-canvas": "^2.0.43", "@antv/g-plugin-dragndrop": "^2.0.35", "@antv/graphlib": "^2.0.4", "@antv/hierarchy": "^0.6.14", "@antv/layout": "1.2.14-beta.9", "@antv/util": "^3.3.10", "bubblesets-js": "^2.3.4"}, "devDependencies": {"@antv/g-svg": "^2.0.38", "@antv/g-webgl": "^2.0.47", "@antv/layout-gpu": "^1.1.7", "@antv/layout-wasm": "^1.4.2", "@antv/vendor": "^1.0.11", "@types/hull.js": "^1.0.4", "@types/xmlserializer": "^0.6.6", "cross-env": "^7.0.3", "jest-canvas-mock": "^2.5.2", "jest-random-mock": "^1.0.0", "xmlserializer": "^0.6.1"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "limit-size": [{"gzip": true, "limit": "330 Kb", "path": "dist/g6.min.js"}, {"limit": "1.1 Mb", "path": "dist/g6.min.js"}], "scripts": {"build": "run-p build:*", "build:cjs": "rimraf ./lib && tsc --module commonjs --outDir lib -p tsconfig.build.json", "build:dev:watch": "npm run build:esm -- --watch", "build:esm": "rimraf ./esm && tsc --module ESNext --outDir esm -p tsconfig.build.json", "build:umd": "rimraf ./dist && rollup -c && npm run size", "bundle-vis": "cross-env BUNDLE_VIS=1 npm run build:umd", "ci": "run-s lint type-check build test", "coverage": "jest --coverage", "coverage:open": "open coverage/lcov-report/index.html", "dev": "vite", "fix": "eslint ./src ./__tests__ --fix && prettier ./src __tests__ --write ", "jest": "node --expose-gc --max-old-space-size=1024 --unhandled-rejections=strict ../../node_modules/jest/bin/jest --coverage --logHeapUsage --detectOpenHandles", "lint": "eslint ./src __tests__ --quiet && prettier ./src __tests__ --check", "readme": "cp ../../README.* ./", "size": "limit-size", "start": "rimraf ./lib && tsc --module commonjs --outDir lib --watch", "tag": "node ./scripts/tag.mjs", "test": "jest", "test:integration": "npm run jest __tests__/integration", "test:unit": "npm run jest __tests__/unit", "type-check": "tsc --noEmit", "version": "node ./scripts/version.mjs"}}