{"version": 3, "file": "animation.js", "sourceRoot": "", "sources": ["../../src/utils/animation.ts"], "names": [], "mappings": ";;AAqBA,sDA8BC;AASD,kDA0CC;AAaD,8CAkBC;AAUD,kDAWC;AA8BD,gEAoCC;AA3ND,qCAAsD;AAEtD,4CAAgH;AAChH,yCAA+C;AAI/C,mCAAgC;AAChC,mCAAkC;AAIlC;;;;;;;GAOG;AACH,SAAgB,qBAAqB,CAAC,KAAgC,EAAE,KAAoB;IAC1F,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,IAAI,CAAC;IAE5D,MAAM,eAAe,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IAChE,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;IAE7E,OAAO,IAAI,KAAK,CAAC,eAAe,EAAE;QAChC,GAAG,CAAC,MAAM,EAAE,OAAyB;YACnC,IAAI,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,IAAI,CAAC,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACxF,OAAO,CAAC,GAAG,IAAe,EAAE,EAAE;oBAC3B,MAAM,CAAC,OAAO,CAAS,CAAC,GAAG,IAAI,CAAC,CAAC;oBAClC,gBAAgB,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE,WAAC,OAAA,MAAC,SAAS,CAAC,OAAO,CAAS,0DAAG,GAAG,IAAI,CAAC,CAAA,EAAA,CAAC,CAAC;gBAClF,CAAC,CAAC;YACJ,CAAC;YACD,IAAI,OAAO,KAAK,UAAU,EAAE,CAAC;gBAC3B,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,eAAe,CAAC,QAAQ,EAAE,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC7G,CAAC;YACD,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACtC,CAAC;QACD,GAAG,CAAC,MAAM,EAAE,OAAyB,EAAE,KAAK;YAC1C,uCAAuC;YACvC,6GAA6G;YAC7G,IAAI,CAAC,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC/C,gBAAgB,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;oBACpC,SAAS,CAAC,OAAO,CAAS,GAAG,KAAK,CAAC;gBACtC,CAAC,CAAC,CAAC;YACL,CAAC;YACD,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QAC7C,CAAC;KACF,CAAC,CAAC;AACL,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,mBAAmB,CAAC,SAAqB;IACvD,wCAAwC;IACxC,uEAAuE;IACvE,MAAM,wBAAwB,GAA0B,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE;QACnF,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;YAC1C,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,SAAS;gBAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;;gBAC1C,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QACH,OAAO,GAAG,CAAC;IACb,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,qCAAqC;IACrC,sGAAsG;IACtG,MAAM,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,EAAE;QACjE;QACE,0DAA0D;QAC1D,MAAM,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM;YAClC,2CAA2C;YAC3C,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAA,YAAK,EAAC,KAAK,CAAC,CAAC;YACpC,2DAA2D;YAC3D,2DAA2D;YAC3D,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAA,cAAO,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EACjH,CAAC;YACD,OAAO,wBAAwB,CAAC,GAAG,CAAC,CAAC;QACvC,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,6CAA6C;IAC7C,sDAAsD;IACtD,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,EAAE;QACpF,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YAC9B,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;gBAAE,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC;;gBAC1C,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QAC/B,CAAC,CAAC,CAAC;QACH,OAAO,GAAG,CAAC;IACb,CAAC,EAAE,EAAgB,CAAC,CAAC;IAErB,uCAAuC;IACvC,yHAAyH;IACzH,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;QAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAExF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;;;;;GAUG;AACH,SAAgB,iBAAiB,CAAC,IAAY;IAC5C,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,SAAS;YACZ,OAAO,CAAC,CAAC;QACX,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,QAAQ;YACX,OAAO,CAAC,CAAC;QACX,KAAK,YAAY;YACf,OAAO,SAAS,CAAC;QACnB,KAAK,WAAW;YACd,OAAO,KAAK,CAAC;QACf,KAAK,QAAQ;YACX,OAAO,EAAE,CAAC;QACZ;YACE,OAAO,SAAS,CAAC;IACrB,CAAC;AACH,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,mBAAmB,CACjC,OAAqB,EACrB,cAA2D;IAE3D,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;IAC9B,IAAI,SAAS,KAAK,KAAK,IAAI,cAAc,KAAK,KAAK;QAAE,OAAO,KAAK,CAAC;IAElE,MAAM,YAAY,qBAA+B,qCAAyB,CAAE,CAAC;IAC7E,IAAI,IAAA,eAAQ,EAAC,SAAS,CAAC;QAAE,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;IAChE,IAAI,IAAA,eAAQ,EAAC,cAAc,CAAC;QAAE,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;IAC1E,OAAO,YAAY,CAAC;AACtB,CAAC;AAED;;;;;;GAMG;AACH,SAAS,WAAW,CAAC,OAAoC;IACvD,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QAChC,MAAM,SAAS,GAAG,IAAA,kBAAY,EAAC,6BAAiB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACrE,IAAI,SAAS;YAAE,OAAO,SAAS,CAAC;QAEhC,aAAK,CAAC,IAAI,CAAC,oBAAoB,OAAO,qBAAqB,CAAC,CAAC;QAC7D,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;;;;;;;GASG;AACH,SAAgB,0BAA0B,CACxC,OAAqB,EACrB,WAAwB,EACxB,KAAqB,EACrB,cAAgD;;IAEhD,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,OAAO,CAAC;IAC/C,IAAI,eAAe,KAAK,KAAK,IAAI,cAAc,KAAK,KAAK;QAAE,OAAO,EAAE,CAAC;IAErE,MAAM,oBAAoB,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAG,WAAW,CAAC,0CAAE,SAAS,CAAC;IAC/D,IAAI,oBAAoB,KAAK,KAAK;QAAE,OAAO,EAAE,CAAC;IAC9C,MAAM,wBAAwB,GAAG,oBAAoB,aAApB,oBAAoB,uBAApB,oBAAoB,CAAG,KAAK,CAAC,CAAC;IAC/D,IAAI,wBAAwB,KAAK,KAAK;QAAE,OAAO,EAAE,CAAC;IAElD,mDAAmD;IACnD,iLAAiL;IAEjL,MAAM,qBAAqB,GAAG,MAAA,IAAA,eAAO,EAAC,OAAO,CAAC,CAAC,WAAW,CAAC,0CAAE,SAAS,CAAC;IAEvE,MAAM,OAAO,GAAG,CAAC,IAAiC,EAAE,EAAE,EAAE,CACtD,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,6DAC7B,8CAAkC,GAClC,CAAC,IAAA,eAAQ,EAAC,eAAe,CAAC,IAAI,eAAe,CAAC,GAC9C,SAAS,GACT,CAAC,IAAA,eAAQ,EAAC,cAAc,CAAC,IAAI,cAAc,CAAC,EAC/C,CAAC,CAAC;IAEN,IAAI,wBAAwB;QAAE,OAAO,OAAO,CAAC,wBAAwB,CAAC,CAAC;IAEvE,IAAI,CAAC,qBAAqB;QAAE,OAAO,EAAE,CAAC;IAEtC,cAAc;IACd,gEAAgE;IAChE,MAAM,0BAA0B,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;IAChE,IAAI,0BAA0B,KAAK,KAAK;QAAE,OAAO,EAAE,CAAC;IACpD,OAAO,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAC7C,CAAC"}