{"version": 3, "file": "prefix.js", "sourceRoot": "", "sources": ["../../src/utils/prefix.ts"], "names": [], "mappings": ";;AAWA,gCAIC;AAUD,8BAEC;AAWD,oCAKC;AAUD,sCAsBC;AAUD,8BAaC;AAUD,wCASC;AAWD,sCAYC;AA5ID,qCAAoD;AAGpD;;;;;;;GAOG;AACH,SAAgB,UAAU,CAAC,GAAW,EAAE,MAAc;IACpD,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC;QAAE,OAAO,KAAK,CAAC;IAC1C,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACrC,OAAO,SAAS,IAAI,GAAG,IAAI,SAAS,IAAI,GAAG,CAAC;AAC9C,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,SAAS,CAAC,GAAW,EAAE,MAAc;IACnD,OAAO,GAAG,MAAM,GAAG,IAAA,iBAAU,EAAC,GAAG,CAAC,EAAE,CAAC;AACvC,CAAC;AAED;;;;;;;;GAQG;AACH,SAAgB,YAAY,CAAC,MAAc,EAAE,MAAe,EAAE,uBAAgC,IAAI;IAChG,IAAI,CAAC,MAAM;QAAE,OAAO,MAAM,CAAC;IAC3B,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC;QAAE,OAAO,MAAM,CAAC;IAC/C,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACxC,OAAO,oBAAoB,CAAC,CAAC,CAAC,IAAA,iBAAU,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AACtD,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,aAAa,CAAgC,KAAa,EAAE,MAAc;IACxF,MAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;QAClE,IAAI,GAAG,KAAK,WAAW,IAAI,GAAG,KAAK,OAAO;YAAE,OAAO,GAAG,CAAC;QACvD,IAAI,UAAU,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,CAAC;YAC5B,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;QAC7D,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC,EAAE,EAAO,CAAC,CAAC;IAEZ,0BAA0B;IAC1B,iFAAiF;IACjF,IAAI,SAAS,IAAI,KAAK,EAAE,CAAC;QACvB,MAAM,aAAa,GAAG,SAAS,CAAC,SAAS,EAAE,MAAM,CAAuB,CAAC;QACzE,MAAM,OAAO,GAAG,KAAK,CAAC,OAAiB,CAAC;QAExC,IAAI,aAAa,IAAI,KAAK,EAAE,CAAC;YAC3B,MAAM,UAAU,GAAG,KAAK,CAAC,aAAa,CAAW,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,OAAO,GAAG,UAAU,EAAE,CAAC,CAAC;QAC7D,CAAC;;YAAM,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,SAAS,CAAC,GAAwB,EAAE,MAAc;IAChE,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC;IAEnC,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAC5B,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QACX,IAAI,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3B,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YACvC,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;QACzB,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC,EACD,EAAyB,CAC1B,CAAC;AACJ,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,cAAc,CAAgC,KAA0B,EAAE,MAAyB;IACjH,MAAM,WAAW,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACnE,MAAM,SAAS,GAAwB,EAAE,CAAC;IAC1C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;QACjC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAChD,SAAS,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC,CAAC,CAAC;IACH,OAAO,SAAc,CAAC;AACxB,CAAC;AAED;;;;;;;;GAQG;AACH,SAAgB,aAAa,CAAmB,KAAQ,EAAE,SAAiB,EAAE,SAAiB;IAC5F,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,CACjC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;QACpB,IAAI,UAAU,CAAC,GAAG,EAAE,SAAS,CAAC,EAAE,CAAC;YAC/B,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,GAAG,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE,SAAS,CAAqB,CAAC,GAAG,KAAK,CAAC;QAC7F,CAAC;aAAM,CAAC;YACN,GAAG,CAAC,GAAuB,CAAC,GAAG,KAAK,CAAC;QACvC,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC,EACD,EAA0D,CAC3D,CAAC;AACJ,CAAC"}