{"version": 3, "file": "shortcut.js", "sourceRoot": "", "sources": ["../../src/utils/shortcut.ts"], "names": [], "mappings": ";;;AAEA,qCAA+C;AAC/C,4CAA2C;AAE3C,mCAAuC;AAQvC,MAAM,aAAa,GAAG,CAAC,IAAiB,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,IAAA,eAAQ,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAEhH,MAAa,QAAQ;IASnB,YAAY,OAAqB;QARzB,QAAG,GAA8B,IAAI,GAAG,EAAE,CAAC;QAE3C,qBAAgB,GAAkB,GAAG,EAAE,GAAE,CAAC,CAAC;QAI3C,cAAS,GAAG,IAAI,GAAG,EAAU,CAAC;QAgD9B,cAAS,GAAG,CAAC,KAAoB,EAAE,EAAE;YAC3C,IAAI,CAAC,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,GAAG,CAAA;gBAAE,OAAO;YACxB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC9B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC,CAAC;QAEM,YAAO,GAAG,CAAC,KAAoB,EAAE,EAAE;YACzC,IAAI,CAAC,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,GAAG,CAAA;gBAAE,OAAO;YACxB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACnC,CAAC,CAAC;QA8BM,YAAO,GAAG,CAAC,KAAiB,EAAE,EAAE;YACtC,IAAI,CAAC,gBAAgB,CAAC,uBAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC;QAEM,WAAM,GAAG,CAAC,KAA0B,EAAE,EAAE;YAC9C,IAAI,CAAC,gBAAgB,CAAC,uBAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACjD,CAAC,CAAC;QAEM,gBAAW,GAAkB,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACtD,IAAI,CAAC,gBAAgB,CAAC,uBAAW,CAAC,KAAK,kCAAO,KAAK,GAAK,OAAO,EAAG,CAAC;QACrE,CAAC,CAAC;QAEM,YAAO,GAAG,GAAG,EAAE;YACrB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACzB,CAAC,CAAC;QAlGA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAEM,IAAI,CAAC,GAAgB,EAAE,OAAgB;QAC5C,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAC7B,IAAI,GAAG,CAAC,QAAQ,CAAC,uBAAW,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YAC1D,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,IAAI,CAAC,YAAY,GAAG,IAAI,oBAAY,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACzF,CAAC;QACD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAC7B,CAAC;IAEM,MAAM,CAAC,GAAgB,EAAE,OAAiB;QAC/C,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACxB,IAAI,IAAA,cAAO,EAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;gBACpB,IAAI,CAAC,OAAO,IAAI,OAAO,KAAK,CAAC;oBAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACpD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,SAAS;QACd,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;IACnB,CAAC;IAEM,KAAK,CAAC,GAAgB;QAC3B,KAAK;QACL,MAAM,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACvE,MAAM,OAAO,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;QAC1C,OAAO,IAAA,cAAO,EAAC,aAAa,EAAE,OAAO,CAAC,CAAC;IACzC,CAAC;IAEO,UAAU;;QAChB,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;QAEzB,OAAO,CAAC,EAAE,CAAC,uBAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACjD,OAAO,CAAC,EAAE,CAAC,uBAAW,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7C,OAAO,CAAC,EAAE,CAAC,uBAAW,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5C,OAAO,CAAC,EAAE,CAAC,uBAAW,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAE1C,yBAAyB;QACzB,4EAA4E;QAC5E,MAAA,UAAU,CAAC,gBAAgB,2DAAG,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IACvD,CAAC;IAaO,OAAO,CAAC,KAAoB;QAClC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE;YAChC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;gBAAE,OAAO,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACK,gBAAgB,CAAC,SAAsB,EAAE,KAAc;QAC7D,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE;YAChC,IAAI,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5B,IACE,IAAA,cAAO,EACL,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAC1B,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,SAAS,CAAC,CACnC,EACD,CAAC;oBACD,OAAO,CAAC,KAAK,CAAC,CAAC;gBACjB,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAkBM,OAAO;;QACZ,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACvD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAW,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACnD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAW,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAW,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAChD,MAAA,IAAI,CAAC,YAAY,0CAAE,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC3D,MAAA,UAAU,CAAC,mBAAmB,2DAAG,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IACzD,CAAC;CACF;AAvHD,4BAuHC"}