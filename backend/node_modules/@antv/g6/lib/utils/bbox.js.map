{"version": 3, "file": "bbox.js", "sourceRoot": "", "sources": ["../../src/utils/bbox.ts"], "names": [], "mappings": ";;AAcA,oCAEC;AASD,sCAEC;AAOD,kCAEC;AAUD,kCAGC;AASD,oCAKC;AAUD,0CAOC;AASD,0CAgBC;AAUD,oCAOC;AAUD,sCAEC;AAWD,sDAUC;AAUD,gDAEC;AAUD,8CAGC;AAUD,wDAUC;AAUD,0DA0BC;AAQD,8CAmBC;AAQD,8CAOC;AASD,0CAeC;AA9SD,+BAA+B;AAC/B,qCAAmC;AAEnC,6BAA+B;AAC/B,iCAAmC;AACnC,uCAAyC;AAEzC;;;;;;GAMG;AACH,SAAgB,YAAY,CAAC,IAAU;IACrC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACnC,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,aAAa,CAAC,IAAU;IACtC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACnC,CAAC;AAED;;;;GAIG;AACH,SAAgB,WAAW,CAAC,IAAU;IACpC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;AACnD,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,WAAW,CAAC,IAAkB,EAAE,OAAiB;IAC/D,MAAM,IAAI,GAAG,IAAA,YAAO,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,CAAC;IACnF,OAAO,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AACzD,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,YAAY,CAAC,KAAY;IACvC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;IAC5B,MAAM,IAAI,GAAG,IAAI,QAAI,EAAE,CAAC;IACxB,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrC,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,eAAe,CAAC,IAAU,EAAE,OAAgB;IAC1D,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,IAAA,sBAAY,EAAC,OAAO,CAAC,CAAC;IACzD,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;IACpC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;IACpC,MAAM,KAAK,GAAG,IAAI,QAAI,EAAE,CAAC;IACzB,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,KAAK,EAAE,IAAI,GAAG,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IACtF,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,eAAe,CAAC,MAAc;IAC5C,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,IAAI,QAAI,EAAE,CAAC;IAC3C,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;IAE1C,MAAM,IAAI,GAAG,IAAI,QAAI,EAAE,CAAC;IACxB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAE7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACvC,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACrB,IAAI,CAAC,SAAS,CACZ,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EACtG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CACvG,CAAC;IACJ,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,YAAY,CAAC,KAAW,EAAE,KAAW;IACnD,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC;IACjC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC;IACjC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC;IACjC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC;IAEjC,OAAO,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC;AAC9E,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,aAAa,CAAC,KAAY,EAAE,IAAU;IACpD,OAAO,IAAA,gBAAS,EAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,IAAA,gBAAS,EAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACxG,CAAC;AAED;;;;;;;;GAQG;AACH,SAAgB,qBAAqB,CAAC,KAAY,EAAE,IAAU,EAAE,QAAQ,GAAG,KAAK;IAC9E,MAAM,EACJ,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EACjB,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,GAClB,GAAG,IAAI,CAAC;IAET,MAAM,iBAAiB,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAA,gBAAS,EAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IACpH,MAAM,iBAAiB,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAA,gBAAS,EAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IAEpH,OAAO,iBAAiB,IAAI,iBAAiB,CAAC;AAChD,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,kBAAkB,CAAC,KAAY,EAAE,IAAU;IACzD,OAAO,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACrC,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,iBAAiB,CAAC,KAAY,EAAE,IAAU;IACxD,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;IACxB,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC;AAC1D,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,sBAAsB,CAAC,CAAQ,EAAE,IAAU;IACzD,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IACjB,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;IAC9B,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;IAC9B,MAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;IACtB,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;IACvB,MAAM,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;IACrB,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,CAAC;IACxB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;IAC/C,OAAO,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC;AACpH,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,uBAAuB,CAAC,CAAQ,EAAE,IAAU;IAC1D,MAAM,GAAG,GAAG,IAAA,YAAK,EAAC,CAAC,CAAC,CAAC;IACrB,IAAI,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC;QAC3B,MAAM,IAAI,GAAG,sBAAsB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QAC7C,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,MAAM;gBACT,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACrB,MAAM;YACR,KAAK,OAAO;gBACV,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACrB,MAAM;YACR,KAAK,KAAK;gBACR,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACrB,MAAM;YACR,KAAK,QAAQ;gBACX,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACrB,MAAM;QACV,CAAC;IACH,CAAC;SAAM,CAAC;QACN,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;QACjB,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;QAC9B,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;QAC9B,GAAG,CAAC,CAAC,CAAC,GAAG,IAAA,gBAAS,EAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;QAC/D,GAAG,CAAC,CAAC,CAAC,GAAG,IAAA,gBAAS,EAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;IACjE,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;;;GAKG;AACH,SAAgB,iBAAiB,CAAC,IAAU,EAAE,SAA4B;IACxE,gBAAgB;IAChB,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;IACxB,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;IAE1C,MAAM,CAAC,GACL,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK,MAAM;QACxC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,SAAS,KAAK,OAAO;YACrB,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC;YACvB,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;IAC9B,MAAM,CAAC,GACL,SAAS,KAAK,MAAM,IAAI,SAAS,KAAK,OAAO;QAC3C,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,SAAS,KAAK,MAAM;YACpB,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC;YACxB,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;IAE/B,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAChB,CAAC;AAED;;;;;GAKG;AACH,SAAgB,iBAAiB,CAAC,IAAU,EAAE,SAA4B;IACxE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;IAE/B,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAEtE,YAAY;IACZ,OAAO,CAAC,SAAA,CAAC,EAAI,CAAC,CAAA,GAAG,SAAA,CAAC,IAAI,CAAC,IAAI,CAAC,SAAA,CAAC,CAAC,GAAG,CAAC,CAAC,EAAI,CAAC,CAAA,GAAG,SAAA,CAAC,EAAI,CAAC,CAAA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAI,CAAC,CAAA,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9E,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,eAAe,CAAC,IAAU;IACxC,MAAM,EACJ,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EACjB,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,GAClB,GAAG,IAAI,CAAC;IACT,MAAM,aAAa,GAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC1C,MAAM,cAAc,GAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC3C,MAAM,iBAAiB,GAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC9C,MAAM,gBAAgB,GAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAE7C,MAAM,GAAG,GAAG,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;IAC5C,MAAM,KAAK,GAAG,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;IAClD,MAAM,MAAM,GAAG,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,CAAC;IACrD,MAAM,IAAI,GAAG,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;IAC/C,OAAO,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAqB,CAAC;AACxD,CAAC"}