{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/transforms/utils.ts"], "names": [], "mappings": ";;AAcA,gCAgBC;AAUD,oCAEC;AAzCD,oCAAmC;AAGnC;;;;;;;;;GASG;AACH,SAAgB,UAAU,CACxB,KAAe,EACf,IAAiC,EACjC,WAAwB,EACxB,KAAmB,EACnB,SAAmB;IAEnB,MAAM,EAAE,GAAG,IAAA,SAAI,EAAC,KAAK,CAAC,CAAC;IACvB,MAAM,QAAQ,GAAG,GAAG,WAAW,GAA0B,CAAC;IAC1D,MAAM,UAAU,GAAQ,SAAS;QAC/B,CAAC,CAAC,KAAK;QACP,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC;IAC7G,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,EAAE;QAC/C,IAAI,IAAI,KAAK,KAAK;YAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;;YACnD,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,YAAY,CAAC,KAA8B,EAAE,aAAsC;IACjG,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9E,CAAC"}