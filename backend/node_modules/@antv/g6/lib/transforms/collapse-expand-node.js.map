{"version": 3, "file": "collapse-expand-node.js", "sourceRoot": "", "sources": ["../../src/transforms/collapse-expand-node.ts"], "names": [], "mappings": ";;;AAAA,4CAAwC;AAGxC,4DAAsD;AACtD,oCAAmC;AACnC,qDAAiD;AAEjD,mCAAqC;AAErC,uBAAuB;AACvB,iEAAiE;AACjE,MAAM,YAAY,GAAG,CAAC,KAAe,EAAE,IAAsB,EAAE,WAAwB,EAAE,KAAmB,EAAE,EAAE;IAC9G,MAAM,QAAQ,GAAG,GAAG,WAAW,GAA0B,CAAC;IAC1D,MAAM,EAAE,GAAG,IAAA,SAAI,EAAC,KAAK,CAAC,CAAC;IACvB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;QACpE,KAAK,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAA,SAAI,EAAC,KAAK,CAAC,EAAE,KAAY,CAAC,CAAC;IACvD,CAAC;AACH,CAAC,CAAC;AAEF;;;;GAIG;AACH,MAAa,kBAAmB,SAAQ,8BAAa;IAC3C,UAAU,CAAC,EAAM;QACvB,OAAO,IAAI,CAAC,OAAO,CAAC,OAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;IAC9C,CAAC;IAEO,YAAY,CAAC,IAAc,EAAE,KAAe;QAClD,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QACzC,IAAI,IAAA,4BAAW,EAAC,IAAI,CAAC;YAAE,OAAO;QAE9B,MAAM,EAAE,GAAG,IAAA,SAAI,EAAC,IAAI,CAAC,CAAC;QACtB,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QAEzC,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;QAChE,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YAC5B,IAAA,kBAAU,EAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;QACxD,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACzB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,UAAU,CAAC,KAAe;QAC/B,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAEtC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,gBAAgB,CAAC,oBAAQ,CAAC;YAAE,OAAO,KAAK,CAAC;QAE1D,MAAM,EACJ,GAAG,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,EAC7C,MAAM,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,GACjC,GAAG,KAAK,CAAC;QACV,MAAM,eAAe,GAAG,IAAI,GAAG,EAAgB,CAAC;QAChD,MAAM,aAAa,GAAG,IAAI,GAAG,EAAgB,CAAC;QAE9C,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE;YAC9B,IAAI,IAAA,4BAAW,EAAC,IAAI,CAAC;gBAAE,eAAe,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,gCAAgC;QAChC,6FAA6F;QAC7F,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YAC1B,IAAI,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,MAAM;gBAAE,OAAO;YACzD,MAAM,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9C,IAAI,IAAA,4BAAW,EAAC,MAAM,CAAC;gBAAE,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE;YACjC,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YACxC,IAAI,CAAC,WAAW;gBAAE,OAAO;YAEzB,MAAM,kBAAkB,GAAG,WAAW,CAAC,UAAU,CAAC,SAAS,CAAC;YAC5D,IAAI,IAAA,4BAAW,EAAC,IAAI,CAAC,EAAE,CAAC;gBACtB,IAAI,CAAC,kBAAkB;oBAAE,eAAe,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;YACzD,CAAC;iBAAM,CAAC;gBACN,IAAI,kBAAkB;oBAAE,aAAa,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;YACtD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,IAAI,GAAG,EAAM,CAAC;QAEnC,eAAe,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE;YACnC,uBAAuB;YACvB,4EAA4E;YAC5E,MAAM,WAAW,GAAG,KAAK,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC;YACjD,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBACjC,MAAM,EAAE,GAAG,IAAA,SAAI,EAAC,UAAU,CAAC,CAAC;gBAC5B,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;oBAAE,OAAO;gBAEjC,IAAA,kBAAU,EAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;gBAChD,MAAM,YAAY,GAAG,KAAK,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;gBACnD,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;oBAC5B,IAAA,kBAAU,EAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;gBAC5C,CAAC,CAAC,CAAC;gBAEH,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE;YACjC,MAAM,SAAS,GAAG,KAAK,CAAC,gBAAgB,CAAC,EAAE,EAAE,oBAAQ,CAAC,CAAC;YAEvD,qBAAqB;YACrB,gEAAgE;YAChE,IAAI,SAAS,CAAC,IAAI,CAAC,4BAAW,CAAC,EAAE,CAAC;gBAChC,IAAA,kBAAU,EAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;gBAC1C,OAAO;YACT,CAAC;YAED,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AA9FD,gDA8FC"}