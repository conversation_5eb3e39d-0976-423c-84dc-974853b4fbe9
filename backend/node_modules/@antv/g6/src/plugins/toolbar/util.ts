import type { CornerPlacement } from '../../types';

/**
 * <zh/> 工具栏显示项目。
 *
 * <en/> The item of the toolbar.
 */
export interface ToolbarItem {
  /**
   * <zh/> 可以使用 id 来配置内置的工具栏项，可以是 'zoom-in'、'zoom-out'、'auto-fit'、'reset' 等值，也可以配合三方的 iconfont 使用，原理是通过 id 来匹配内置的 svg symbol。See: https://developer.mozilla.org/en-US/docs/Web/SVG/Element/symbol。
   *
   * <en/> You can use id to configure the built-in toolbar items, which can be values such as 'zoom-in', 'zoom-out', 'auto-fit', 'reset', etc. One of the two configurations with `marker`. The principle is to match the built-in svg symbol through id. See: https://developer.mozilla.org/en-US/docs/Web/SVG/Element/symbol.
   */
  readonly id: 'zoom-in' | 'zoom-out' | 'redo' | 'undo' | 'edit' | 'delete' | 'auto-fit' | 'export' | 'reset' | string;
  /**
   * <zh/> 工具栏项对应的值，在 onClick 中作为回调参数。
   *
   * <en/> The value corresponding to the toolbar item, used as a callback parameter in `onClick`.
   */
  readonly value: string;
  /**
   * <en/> The title of the toolbar item. The title will be displayed as a tooltip when the mouse hovers over the item.
   */
  readonly title?: string;
}

/**
 * <zh/> 解析 toolbar 的 position 为位置样式。
 *
 * <en/> Parse the position of the toolbar into position style.
 * @param position - position
 * @returns style
 */
export function parsePositionToStyle(position: CornerPlacement): Partial<CSSStyleDeclaration> {
  const style: Partial<CSSStyleDeclaration> = {
    top: 'unset',
    right: 'unset',
    bottom: 'unset',
    left: 'unset',
  };

  const directions = position.split('-') as ('top' | 'right' | 'bottom' | 'left')[];
  directions.forEach((d) => {
    style[d] = '8px';
  });

  style.flexDirection = position.startsWith('top') || position.startsWith('bottom') ? 'row' : 'column';

  return style;
}

/**
 * 内置默认的 toolbar 的 CSS 样式。
 */
export const TOOLBAR_CSS = `
  .g6-toolbar {
    position: absolute;
    z-index: 100;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
    opacity: 0.65;
  }
  .g6-toolbar .g6-toolbar-item {
    display: inline-block;
    width: 16px;
    height: 16px;
    padding: 4px;
    cursor: pointer;
    box-sizing: content-box;
  }

  .g6-toolbar .g6-toolbar-item:hover {
    background-color: #f0f0f0;
  }

  .g6-toolbar .g6-toolbar-item svg {
    display: inline-block;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }
`;

export const BUILD_IN_SVG_ICON = `
  <svg>
    <symbol id="zoom-in" viewBox="64 64 896 896">
      <path d="M637 443H519V309c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v134H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h118v134c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V519h118c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"></path>
    </symbol>
    <symbol id="zoom-out" viewBox="64 64 896 896">
      <path d="M637 443H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h312c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"></path>
    </symbol>
    <symbol id="edit" viewBox="64 64 896 896">
      <path d="M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"></path>
    </symbol>
    <symbol id="delete" viewBox="64 64 896 896">
      <path d="M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"></path>
    </symbol>
    <symbol id="redo" viewBox="64 64 896 896">
      <path d="M758.2 839.1C851.8 765.9 912 651.9 912 523.9 912 303 733.5 124.3 512.6 124 291.4 123.7 112 302.8 112 523.9c0 125.2 57.5 236.9 147.6 310.2 3.5 2.8 8.6 2.2 11.4-1.3l39.4-50.5c2.7-3.4 2.1-8.3-1.2-11.1-8.1-6.6-15.9-13.7-23.4-21.2a318.64 318.64 0 01-68.6-101.7C200.4 609 192 567.1 192 523.9s8.4-85.1 25.1-124.5c16.1-38.1 39.2-72.3 68.6-101.7 29.4-29.4 63.6-52.5 101.7-68.6C426.9 212.4 468.8 204 512 204s85.1 8.4 124.5 25.1c38.1 16.1 72.3 39.2 101.7 68.6 29.4 29.4 52.5 63.6 68.6 101.7 16.7 39.4 25.1 81.3 25.1 124.5s-8.4 85.1-25.1 124.5a318.64 318.64 0 01-68.6 101.7c-9.3 9.3-19.1 18-29.3 26L668.2 724a8 8 0 00-14.1 3l-39.6 162.2c-1.2 5 2.6 9.9 7.7 9.9l167 .8c6.7 0 10.5-7.7 6.3-12.9l-37.3-47.9z"></path>
    </symbol>
    <symbol id="undo" viewBox="64 64 896 896">
      <path d="M511.4 124C290.5 124.3 112 303 112 523.9c0 128 60.2 242 153.8 315.2l-37.5 48c-4.1 5.3-.3 13 6.3 12.9l167-.8c5.2 0 9-4.9 7.7-9.9L369.8 727a8 8 0 00-14.1-3L315 776.1c-10.2-8-20-16.7-29.3-26a318.64 318.64 0 01-68.6-101.7C200.4 609 192 567.1 192 523.9s8.4-85.1 25.1-124.5c16.1-38.1 39.2-72.3 68.6-101.7 29.4-29.4 63.6-52.5 101.7-68.6C426.9 212.4 468.8 204 512 204s85.1 8.4 124.5 25.1c38.1 16.1 72.3 39.2 101.7 68.6 29.4 29.4 52.5 63.6 68.6 101.7 16.7 39.4 25.1 81.3 25.1 124.5s-8.4 85.1-25.1 124.5a318.64 318.64 0 01-68.6 101.7c-7.5 7.5-15.3 14.5-23.4 21.2a7.93 7.93 0 00-1.2 11.1l39.4 50.5c2.8 3.5 7.9 4.1 11.4 1.3C854.5 760.8 912 649.1 912 523.9c0-221.1-179.4-400.2-400.6-399.9z"></path>
    </symbol>
    <symbol id="export" viewBox="64 64 896 896">
      <path d="M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM770.87 199.13l-52.2-52.2a8.01 8.01 0 014.7-13.6l179.4-21c5.1-.6 9.5 3.7 8.9 8.9l-21 179.4c-.8 6.6-8.9 9.4-13.6 4.7l-52.4-52.4-256.2 256.2a8.03 8.03 0 01-11.3 0l-42.4-42.4a8.03 8.03 0 010-11.3l256.1-256.3z"></path>
    </symbol>
    <symbol id="auto-fit" viewBox="64 64 896 896">
      <path d="M952 474H829.8C812.5 327.6 696.4 211.5 550 194.2V72c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v122.2C327.6 211.5 211.5 327.6 194.2 474H72c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h122.2C211.5 696.4 327.6 812.5 474 829.8V952c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V829.8C696.4 812.5 812.5 696.4 829.8 550H952c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zM512 756c-134.8 0-244-109.2-244-244s109.2-244 244-244 244 109.2 244 244-109.2 244-244 244z"></path>
      <path d="M512 392c-32.1 0-62.1 12.4-84.8 35.2-22.7 22.7-35.2 52.7-35.2 84.8s12.5 62.1 35.2 84.8C449.9 619.4 480 632 512 632s62.1-12.5 84.8-35.2C619.4 574.1 632 544 632 512s-12.5-62.1-35.2-84.8A118.57 118.57 0 00512 392z"></path>
    </symbol>
    <symbol id="reset" viewBox="64 64 896 896">
      <path d="M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"></path>
    </symbol>
    <symbol id="exit-fullscreen" viewBox="0 0 1024 1024">
      <path d="M418.13333333 361.43786666c0 0.2048-0.13653333 0.4096-0.13653334 0.68266667C417.99679999 362.32533333 418.13333333 362.53013333 418.13333333 362.73493333 418.13333333 371.54133333 414.44693333 379.392 408.78079999 385.39946666 408.43946666 385.7408 408.30293333 386.21866666 408.02986666 386.49173333c-1.09226667 1.09226667-2.59413333 1.77493333-3.82293333 2.73066667C398.40426666 393.65973333 391.64586666 396.8 383.93173333 396.8 383.72693333 396.8 383.59039999 396.73173333 383.38559999 396.73173333S382.97599999 396.8 382.77119999 396.8L112.29866666 396.8C92.50133333 396.8 76.79999999 381.50826666 76.79999999 362.66666666 76.66346666 343.89333333 92.63786666 328.53333333 112.16213333 328.53333333l189.44 0L87.44959999 114.51733333C73.59146666 100.59093333 73.25013333 78.5408 86.63039999 65.29706666c13.17546667-13.44853333 35.36213333-12.97066667 49.152 0.88746667l214.08426667 214.08426667L349.86666666 90.89706666C349.79839999 71.23626666 365.22666666 55.46666666 383.99999999 55.46666666 402.77333333 55.33013333 418.13333333 71.30453333 418.13333333 90.8288L418.13333333 361.43786666zM928.90453333 328.53333333l-189.44 0 214.15253333-214.08426667c13.85813333-13.9264 14.19946667-35.90826667 0.88746667-49.22026666-13.17546667-13.44853333-35.36213333-12.97066667-49.152 0.88746666l-214.08426667 214.08426667L691.26826666 90.89706666C691.26826666 71.23626666 675.83999999 55.46666666 657.06666666 55.46666666 638.29333333 55.33013333 622.93333333 71.30453333 622.93333333 90.8288l0 270.60906666c0 0.2048 0.13653333 0.4096 0.13653333 0.68266667C623.06986666 362.32533333 622.93333333 362.53013333 622.93333333 362.73493333 622.93333333 371.54133333 626.61973333 379.392 632.28586666 385.39946666c0.34133333 0.34133333 0.47786667 0.8192 0.8192 1.09226667 1.09226667 1.09226667 2.59413333 1.77493333 3.8912 2.73066667C642.66239999 393.65973333 649.42079999 396.8 657.13493333 396.8c0.2048 0 0.34133333-0.06826667 0.54613333-0.06826667S658.09066666 396.8 658.29546666 396.8l270.5408 0C948.56533333 396.8 964.26666666 381.50826666 964.26666666 362.66666666 964.40319999 343.89333333 948.42879999 328.53333333 928.90453333 328.53333333zM418.13333333 635.73333333c0-8.8064-3.6864-16.5888-9.35253334-22.66453333C408.43946666 612.72746666 408.30293333 612.2496 408.02986666 611.90826666 406.86933333 610.88426666 405.43573333 610.2016 404.20693333 609.24586666 398.47253333 604.80853333 391.64586666 601.6 383.93173333 601.6 383.72693333 601.6 383.59039999 601.73653333 383.38559999 601.73653333S382.97599999 601.6 382.77119999 601.6L112.29866666 601.6C92.50133333 601.6 76.79999999 616.96 76.79999999 635.73333333 76.66346666 654.50666666 92.63786666 669.86666666 112.16213333 669.86666666l189.44 0-214.15253334 214.15253334c-13.85813333 13.85813333-14.19946667 35.84-0.88746666 49.22026666 13.17546667 13.44853333 35.36213333 12.9024 49.152-0.95573333l214.08426666-214.08426667 0 189.37173334c0 19.59253333 15.42826667 35.49866667 34.2016 35.36213333C402.77333333 943.2064 418.13333333 927.232 418.13333333 907.5712L418.13333333 637.09866666c0-0.27306667-0.13653333-0.47786667-0.13653334-0.68266666C417.99679999 636.14293333 418.13333333 635.93813333 418.13333333 635.73333333zM739.46453333 669.86666666l189.44 0c19.456 0 35.49866667-15.36 35.36213333-34.13333333C964.26666666 616.96 948.56533333 601.6 928.76799999 601.6L658.29546666 601.6C658.09066666 601.6 657.88586666 601.73653333 657.68106666 601.73653333S657.33973333 601.6 657.13493333 601.6C649.42079999 601.6 642.59413333 604.80853333 636.85973333 609.24586666 635.63093333 610.2016 634.19733333 610.88426666 633.03679999 611.90826666 632.76373333 612.2496 632.62719999 612.72746666 632.28586666 613.0688 626.61973333 619.14453333 622.93333333 626.92693333 622.93333333 635.73333333c0 0.2048 0.13653333 0.4096 0.13653333 0.68266667C623.06986666 636.6208 622.93333333 636.8256 622.93333333 637.09866666l0 270.5408C622.93333333 927.232 638.29333333 943.2064 657.06666666 942.93333333c18.77333333 0.13653333 34.2016-15.70133333 34.2016-35.36213333l0-189.37173334 214.08426667 214.08426667c13.78986667 13.85813333 35.90826667 14.40426667 49.152 0.95573333 13.312-13.312 12.97066667-35.36213333-0.88746667-49.22026666L739.46453333 669.86666666z"  ></path></symbol>
    <symbol id="request-fullscreen" viewBox="0 0 1024 1024">
      <path d="M69.818182 87.598545v273.128728a34.909091 34.909091 0 0 0 69.818182 0V163.653818l221.928727 222.021818a33.512727 33.512727 0 0 0 47.383273-47.383272L186.926545 116.363636h197.073455a34.909091 34.909091 0 0 0 0-69.818181H110.871273C85.364364 46.545455 69.818182 59.671273 69.818182 87.598545zM938.542545 46.545455H665.413818a34.909091 34.909091 0 0 0 0 69.818181h197.073455L640.465455 338.292364a33.512727 33.512727 0 0 0 47.383272 47.383272l221.928728-222.021818v197.073455a34.909091 34.909091 0 0 0 69.818181 0V87.598545c0-27.927273-15.453091-41.053091-40.96-41.05309z m-827.671272 907.636363h273.128727a34.909091 34.909091 0 0 0 0-69.818182H186.926545l222.021819-221.928727a33.512727 33.512727 0 0 0-47.383273-47.383273L139.636364 837.073455V640a34.909091 34.909091 0 0 0-69.818182 0v273.128727c0 27.927273 15.546182 41.053091 41.053091 41.053091z m868.724363-41.053091V640a34.909091 34.909091 0 0 0-69.818181 0v197.073455L687.941818 615.051636a33.512727 33.512727 0 0 0-47.383273 47.383273L862.487273 884.363636H665.413818a34.909091 34.909091 0 0 0 0 69.818182h273.128727c25.6 0 41.053091-13.125818 41.053091-41.053091z"  ></path></symbol>
  </svg>
`;
