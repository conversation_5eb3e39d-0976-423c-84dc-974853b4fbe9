import { G2Mark, G2Context } from './types/options';
export declare const CALLBACK_ITEM_SYMBOL: unique symbol;
export declare function applyDefaults(I: number[], mark: G2Mark, context: G2Context): [number[], G2Mark];
export declare function applyDataTransform(I: number[], mark: G2Mark, context: G2Context): Promise<[number[], G2Mark]>;
export declare function flatEncode(I: number[], mark: G2Mark, context: G2Context): [number[], G2Mark];
export declare function inferChannelsType(I: number[], mark: G2Mark, context: G2Context): [number[], G2Mark];
export declare function maybeVisualChannel(I: number[], mark: G2Mark, context: G2Context): [number[], G2Mark];
export declare function extractColumns(I: number[], mark: G2Mark, context: G2Context): [number[], G2Mark];
/**
 * Normalize mark.tooltip to {title, items}.
 */
export declare function normalizeTooltip(I: number[], mark: G2Mark, context: G2Context): [number[], G2Mark];
export declare function extractTooltip(I: number[], mark: G2Mark, context: G2Context): [number[], G2Mark];
export declare function maybeArrayField(I: number[], mark: G2Mark, context: G2Context): [number[], G2Mark];
export declare function addGuideToScale(I: number[], mark: G2Mark, context: G2Context): [number[], G2Mark];
export declare function maybeNonAnimate(I: number[], mark: G2Mark, context: G2Context): [number[], G2Mark];
