import { Coordinate } from '@antv/coord';
import { Layout, G2Theme } from './types/common';
import { G2GuideComponentOptions, G2Library, G2View } from './types/options';
export declare function processAxisZ(components: G2GuideComponentOptions[]): void;
export declare function computeLayout(components: G2GuideComponentOptions[], options: G2View, theme: G2Theme, library: G2Library): Layout;
export declare function computeRoughPlotSize(options: G2View): {
    width: number;
    height: number;
};
export declare function placeComponents(components: G2GuideComponentOptions[], coordinate: Coordinate, layout: Layout): void;
