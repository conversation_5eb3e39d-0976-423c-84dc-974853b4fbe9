import { IDocument } from '@antv/g';
import { G2ComponentOptions, G2Context, G2Library } from './types/options';
import { G2Component, G2ComponentNamespaces, G2ComponentValue } from './types/component';
export declare function useLibrary<O extends G2ComponentOptions, C extends G2Component, V extends G2ComponentValue>(namespace: G2ComponentNamespaces, publicLibrary: G2Library): [(options: O, context?: any) => V, (type: O['type']) => C];
export declare function documentOf(library: G2Context): IDocument;
