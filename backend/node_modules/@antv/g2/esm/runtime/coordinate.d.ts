import { Coordinate } from '@antv/coord';
import { G2View, G2CoordinateOptions, G2Library } from './types/options';
import { Layout } from './types/common';
export declare function createCoordinate(layout: Layout, partialOptions: G2View, library: G2Library): Coordinate;
export declare function coordinate2Transform(node: G2<PERSON>ie<PERSON>, library: G2Library): G2View;
export declare function coordOf(coordinates: G2CoordinateOptions[], type: string): G2CoordinateOptions[];
/**
 * todo Duplication is not considered
 */
export declare function isPolar(coordinates: G2CoordinateOptions[]): boolean;
export declare function isHelix(coordinates: G2CoordinateOptions[]): boolean;
/**
 * todo The number of transposes matters
 */
export declare function isTranspose(coordinates: G2CoordinateOptions[]): boolean;
export declare function isParallel(coordinates: G2CoordinateOptions[]): boolean;
export declare function isTheta(coordinates: G2CoordinateOptions[]): boolean;
export declare function isReflect(coordinates: G2CoordinateOptions[]): boolean;
export declare function isRadial(coordinates: G2CoordinateOptions[]): boolean;
export declare function isRadar(coordinates: G2CoordinateOptions[]): boolean;
/**
 * todo The axis corresponding to the Y reversal is not reversed
 */
export declare function isReflectY(coordinates: G2CoordinateOptions[]): boolean;
