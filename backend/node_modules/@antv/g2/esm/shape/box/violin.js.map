{"version": 3, "file": "violin.js", "sourceRoot": "", "sources": ["../../../src/shape/box/violin.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,EAAE,IAAI,IAAI,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAEtD,OAAO,EAAE,UAAU,EAAE,MAAM,UAAU,CAAC;AACtC,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAE/C,OAAO,EAAE,OAAO,EAAE,MAAM,wBAAwB,CAAC;AACjD,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAItD,SAAS,OAAO,CAAC,CAAY,EAAE,UAAsB,EAAE,IAAI,GAAG,CAAC;IAC7D,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC;IAEtB,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;QACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAErB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,IAAI,CAAC,SAAS,EAAE,CAAC;QAEjB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACtB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEtB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QACrD,IAAI,CAAC,SAAS,EAAE,CAAC;QAEjB,OAAO,IAAI,CAAC;KACb;IAED,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;IACtC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC;IAEtB,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAErC,MAAM,WAAW,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC,CAAC;IACjD,MAAM,UAAU,GAAG,WAAW,GAAG,SAAS,CAAC;IAC3C,MAAM,QAAQ,GAAG,WAAW,GAAG,SAAS,CAAC;IAEzC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAErB,IAAI,CAAC,MAAM,CACT,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,QAAQ,GAAG,CAAC,EACnC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,QAAQ,GAAG,CAAC,CACpC,CAAC;IACF,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;IAC/C,IAAI,CAAC,MAAM,CACT,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,QAAQ,GAAG,CAAC,EACjC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,QAAQ,GAAG,CAAC,CAClC,CAAC;IACF,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;IACrD,IAAI,CAAC,MAAM,CACT,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,QAAQ,GAAG,CAAC,EACnC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,QAAQ,GAAG,CAAC,CACpC,CAAC;IACF,IAAI,CAAC,SAAS,EAAE,CAAC;IAEjB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAEtB,MAAM,CAAC,GAAG,CAAC,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;IAEtC,IAAI,CAAC,MAAM,CACT,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,YAAY,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,EAC3C,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,YAAY,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAC5C,CAAC;IACF,IAAI,CAAC,GAAG,CACN,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,YAAY,GAAG,CAAC,EAC9B,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,YAAY,GAAG,CAAC,EAC9B,IAAI,GAAG,CAAC,EACR,CAAC,EACD,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAChB,CAAC;IACF,IAAI,CAAC,SAAS,EAAE,CAAC;IAEjB,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAM,CAAC,MAAM,MAAM,GAAsB,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;IAC5D,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;IACzC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;QACjC,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC;QACnC,2CAA2C;QAC3C,MAAM,IAAI,GAAG,CAAC,CAAC;QACf,MAAM,EACJ,KAAK,EAAE,YAAY,EACnB,IAAI,GAAG,YAAY,EACnB,MAAM,GAAG,YAAY,KAEnB,QAAQ,EADP,IAAI,UACL,QAAQ,EALN,2BAKL,CAAW,CAAC;QACb,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QAC/C,OAAO,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;aAC9C,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;aACtB,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC;aAC3B,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC;aACvB,KAAK,CAAC,MAAM,EAAE,KAAK,IAAI,IAAI,CAAC;aAC5B,KAAK,CAAC,WAAW,EAAE,SAAS,CAAC;aAC7B,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC;aACzB,IAAI,EAAE,CAAC;IACZ,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,CAAC,KAAK,GAAG;IACb,aAAa,EAAE,OAAO;IACtB,qBAAqB,EAAE,QAAQ;IAC/B,sBAAsB,EAAE,UAAU;IAClC,oBAAoB,EAAE,SAAS;CAChC,CAAC"}