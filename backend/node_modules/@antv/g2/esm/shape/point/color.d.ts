import { Coordinate, Vector2 } from '@antv/coord';
import { ShapeComponent as SC } from '../../runtime';
export type ColorOptions = {
    colorAttribute: 'fill' | 'stroke';
    symbol: string;
    mode?: 'fixed' | 'auto' | 'normal';
    [key: string]: any;
};
export declare function getRadius(mode: ColorOptions['mode'], points: Vector2[], value: Record<string, any>, coordinate: Coordinate): any;
/**
 * Render point in different coordinate.
 */
export declare const Color: SC<ColorOptions>;
