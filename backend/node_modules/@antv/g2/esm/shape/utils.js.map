{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/shape/utils.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,YAAY,CAAC;AAClD,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAG/C,OAAO,EAAE,OAAO,EAAE,MAAM,gBAAgB,CAAC;AACzC,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAE3D,OAAO,EAAE,KAAK,EAAE,iBAAiB,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,iBAAiB,CAAC;AAEtE,MAAM,UAAU,UAAU,CACxB,SAAoB,EACpB,KAAgC;IAEhC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QAChD,SAAS,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;KAC7B;AACH,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,aAAa,CAAC,IAAY,EAAE,MAAiB;IAC3D,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CACxB,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAC9D,CAAC;IACF,IAAI,CAAC,SAAS,EAAE,CAAC;IACjB,OAAO,IAAI,CAAC;AACd,CAAC;AAaD;;;;;GAKG;AACH,MAAM,UAAU,WAAW,CACzB,IAAa,EACb,EAAW,EACX,OAAqB;IAErB,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;IAC9B,MAAM,IAAI,GACR,OAAO,SAAS,KAAK,QAAQ;QAC3B,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC;QACjD,CAAC,CAAC,SAAS,CAAC;IAChB,8BAA8B;IAC9B,+BAA+B;IAC/B,MAAM,UAAU,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;IAE/B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAE3D,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,UAAU,CAAC;IACrD,MAAM,MAAM,GAAY;QACtB,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC;QACpC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC;KACrC,CAAC;IAEF,MAAM,WAAW,GAAG,KAAK,GAAG,UAAU,CAAC;IACvC,MAAM,MAAM,GAAY;QACtB,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC;QACpC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC;KACrC,CAAC;IAEF,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1B,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,SAAS,CACvB,IAAY,EACZ,IAAa,EACb,EAAW,EACX,MAAe,EACf,MAAc;IAEd,MAAM,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;IACtD,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;IAElD,IAAI,CAAC,GAAG,CACN,MAAM,CAAC,CAAC,CAAC,EACT,MAAM,CAAC,CAAC,CAAC,EACT,MAAM,EACN,UAAU,EACV,QAAQ,EACR,QAAQ,GAAG,UAAU,GAAG,CAAC,CAC1B,CAAC;IAEF,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,eAAe,CAC7B,CAAW,EACX,CAAW,EACX,CAAW,EACX,OAAyB,GAAG,EAC5B,OAAoC,SAAS,EAC7C,OAAO,GAAG,KAAK;IAEf,wFAAwF;IACxF,MAAM,QAAQ,GAAG,CAAC,IAAsB,EAAE,OAAgB,EAAE,EAAE;QAC5D,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,IAAI,EAAE;YACjC,IAAI,OAAO,EAAE;gBACX,OAAO,GAAG,CAAC;aACZ;iBAAM;gBACL,OAAO,EAAE,CAAC;aACX;SACF;aAAM;YACL,IAAI,OAAO,EAAE;gBACX,OAAO,EAAE,CAAC;aACX;iBAAM;gBACL,OAAO,CAAC,CAAC;aACV;SACF;IACH,CAAC,CAAC;IAEF,MAAM,CAAC,GAAG,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACtC,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;IAErB,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,2DAA2D;IAC3D,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC;QACnB,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;QAClB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;KAChB,CAAC,CAAC;IAEH,MAAM,UAAU,GAAG,CAAC,CAAC,EAAE,EAAE,CACvB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAE1D,MAAM,YAAY,GAAG;QACnB,2CAA2C;QAC3C,OAAO,EAAE,CAAC,CAAS,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG;QACnD,kEAAkE;QAClE,KAAK,EAAE,CAAC,CAAS,EAAE,EAAE,CACnB,CAAC,KAAK,CAAC;YACL,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG;YAC7B,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG;QAChE,gEAAgE;QAChE,GAAG,EAAE,CAAC,CAAS,EAAE,EAAE,CACjB,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC;YAChB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG;YAC7B,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG;KACjE,CAAC;IAEF,MAAM,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;SAC7D,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,SAAS,CAAC,CAAC;SAClD,IAAI,CAAC,GAAG,CAAC,CAAC;IACb,OAAO,mBAAmB,KAAK,QAAQ,QAAQ,GAAG,CAAC;AACrD,CAAC;AAED,MAAM,UAAU,OAAO,CAAC,MAAiB;IACvC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,MAAM,CAAC;IAChC,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAC1B,CAAC;AAED,MAAM,UAAU,YAAY,CAC1B,UAAsB,EACtB,MAAiB,EACjB,CAAmB;IAEnB,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,AAAD,EAAG,EAAE,CAAC,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IAE1E,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;IAClB,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAa,CAAC;IACjD,MAAM,EAAE,GAAG,iBAAiB,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;IAC9C,MAAM,EAAE,GAAG,iBAAiB,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;IAC9C,2CAA2C;IAC3C,iBAAiB;IACjB,6BAA6B;IAC7B,gCAAgC;IAChC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACzD,OAAO;QACL,UAAU,EAAE,EAAE;QACd,QAAQ,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,EAAE;QAC9C,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC;QAC7B,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC;KAC9B,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,eAAe,CAC7B,KAA0B;IAE1B,MAAM,MAAM,GAAG,SAAS,CAAC;IACzB,OAAO,MAAM,CAAC,WAAW,CACvB,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;SAClB,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;SACzC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC;QACrB,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QAC1C,KAAK;KACN,CAAC;SACD,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,SAAS,CAAC,CACxC,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,OAAO;IAClC,MAAM,EAAE,cAAc,EAAE,gBAAgB,GAAG,cAAc,EAAE,GAAG,OAAO,CAAC;IACtE,OAAO,GAAG,gBAAgB,SAAS,CAAC;AACtC,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,UAAU,EAAE,KAAK;IAC5C,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;QAAE,OAAO,EAAE,CAAC;IACpC,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAa,CAAC;IACjD,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;IACpC,OAAO,aAAa,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,IAAI,EAAE,EAAE,CAAC;AACjE,CAAC;AAED,MAAM,UAAU,SAAS,CAAC,MAA6B;IACrD,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1C,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;IACpD,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AACvD,CAAC"}