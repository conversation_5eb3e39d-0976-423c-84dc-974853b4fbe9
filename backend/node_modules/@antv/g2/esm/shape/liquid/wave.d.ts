import { PathStyleProps, IAnimation } from '@antv/g';
/**
 * Add wave.
 * @param x           center x
 * @param y           center y
 * @param level       wave level 0～1
 * @param waveCount   wave count
 * @param waveAttrs   style
 * @param group       g
 * @param minY        Minimum height
 * @param radius      radius
 * @param waveLength  wave length
 * @param animation  animation config
 * @param document
 */
export declare function addWave(x: number, y: number, level: number, waveCount: number, waveAttrs: PathStyleProps, group: any, minY: number, radius: number, waveLength: number, animation: IAnimation | boolean, document: any): void;
