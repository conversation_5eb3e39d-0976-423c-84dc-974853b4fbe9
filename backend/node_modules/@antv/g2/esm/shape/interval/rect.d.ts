import { ShapeComponent as SC } from '../../runtime';
export type RectOptions = {
    /**
     * Minimum width of each interval.
     */
    minWidth?: number;
    /**
     * Maximum width of each interval.
     */
    maxWidth?: number;
    /**
     * Minimum height of each interval.
     */
    minHeight?: number;
};
/**
 * <PERSON>der rect in different coordinate and using color channel for stroke and fill attribute.
 * The stroke attribute is valid with specified lineWidth attribute which defaults to zero.
 */
export declare const Rect: SC<RectOptions>;
