{"version": 3, "file": "scrollbar.js", "sourceRoot": "", "sources": ["../../src/component/scrollbar.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,+CAAkE;AAUlE;;GAEG;AACI,MAAM,SAAS,GAA0B,CAAC,OAAO,EAAE,EAAE;IAC1D,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,KAAK,KAAc,OAAO,EAAhB,IAAI,UAAK,OAAO,EAAzD,0CAA+C,CAAU,CAAC;IAEhE,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE;QAC3C,MAAM,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;QACvB,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;QACrC,MAAM,EAAE,SAAS,EAAE,cAAc,GAAG,EAAE,EAAE,GAAG,KAAK,CAAC;QACjD,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;QAC5C,MAAM,QAAQ,GAAG,WAAW,KAAK,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;QAC/D,MAAM,UAAU,GAAG,QAAQ,GAAG,KAAK,CAAC;QACpC,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;QACvB,MAAM,MAAM,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,OAAO,IAAI,qBAAkB,CAAC;YAC5B,SAAS,EAAE,cAAc;YACzB,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,cAAc,8DAClC,KAAK,KACR,CAAC;gBACD,CAAC,EACD,WAAW,EAAE,QAAQ,EACrB,KAAK,EAAE,MAAM,KACV,IAAI,KACP,WAAW,EACX,aAAa,EAAE,UAAU,EACzB,cAAc,EAAE,QAAQ,IACxB;SACH,CAA6B,CAAC;IACjC,CAAC,CAAC;AACJ,CAAC,CAAC;AA3BW,QAAA,SAAS,aA2BpB;AAEF,iBAAS,CAAC,KAAK,GAAG;IAChB,eAAe,EAAE,QAAQ;IACzB,WAAW,EAAE,EAAE;IACf,YAAY,EAAE,CAAC;IACf,mBAAmB,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;IAC7B,cAAc,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;CACzB,CAAC"}