import { CompositionComponent as CC } from '../runtime';
import { ViewComposition } from '../spec';
export type ViewOptions = Omit<ViewComposition, 'type'>;
/**
 * @todo Propagate more options to children.(e.g. filter)
 * @todo Propagate encode options to children. This is useful for Matrix composition.
 * @todo Move this to runtime, do not treat it as a composition to cause confusion.
 */
export declare const View: CC<ViewOptions>;
