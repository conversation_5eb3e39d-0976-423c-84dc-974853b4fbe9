{"version": 3, "file": "repeatMatrix.js", "sourceRoot": "", "sources": ["../../src/composition/repeatMatrix.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,qCAAqC;AAQrC,kDAA+C;AAC/C,4CAA2C;AAC3C,0CAAyC;AACzC,2CAMqB;AACrB,mCAAgE;AAIhE,MAAM,QAAQ,GAAG,IAAA,yBAAiB,EAAa,CAAC,OAAO,EAAE,EAAE;IACzD,OAAO;QACL,KAAK,EAAE;YACL,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC,EAAE,YAAY,EAAE,GAAG,EAAE;YACtD,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,YAAY,EAAE,GAAG,EAAE;SACtE;KACF,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,MAAM,WAAW,GAAG,IAAA,0BAAkB,EAAa,CAAC,OAAO,EAAE,EAAE;IAC7D,MAAM,EACJ,IAAI,EACJ,QAAQ,EACR,CAAC,EAAE,OAAO,GAAG,CAAC,EACd,CAAC,EAAE,OAAO,GAAG,CAAC,EACd,GAAG,EAAE,OAAO,GACb,GAAG,OAAO,CAAC;IACZ,MAAM,cAAc,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;QACnD,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;QACvC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC;QAClE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;QAChD,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;QAChD,MAAM,KAAK,GAAG,IAAA,eAAO,EAAC,UAAU,CAAC,CAAC;QAClC,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,IAAA,iBAAQ,EAAC,MAAM,CAAC,CAAC,CAAC;QAC/D,MAAM,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;YAChB,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;SACjB,CAAC,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACvC,WAAW,EAAE,EAAE;YACf,WAAW,EAAE,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YAChC,WAAW,EAAE,EAAE;YACf,kBAAkB,EAAE,OAAO,CAAC,MAAM;YAClC,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YAC7B,QAAQ,EAAE,EAAE;YACZ,eAAe,EAAE,OAAO,CAAC,MAAM;SAChC,CAAC,CAAC,CAAC;QACJ,MAAM,kBAAkB,GAAa,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YACxD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC;gBAAE,OAAO,QAAQ,CAAC;YAC7C,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QACH,OAAO,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YACzB,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAC3B,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACxB,MAAM,QAAQ,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;YACvC,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;;gBACxB,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,KAAc,CAAC,EAAV,IAAI,UAAK,CAAC,EAAtD,iDAAkD,CAAI,CAAC;gBAC7D,MAAM,MAAM,GAAG,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,CAAC,0CAAE,KAAK,CAAC;gBAC/B,MAAM,MAAM,GAAG,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,CAAC,0CAAE,KAAK,CAAC;gBAC/B,MAAM,YAAY,GAAG;oBACnB,uDAAuD;oBACvD,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;oBACnB,uDAAuD;oBACvD,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;iBACpB,CAAC;gBACF,MAAM,OAAO,GAAG;oBACd,CAAC,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC;oBACpC,CAAC,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC;iBACrC,CAAC;gBACF,MAAM,WAAW,GAAG;oBAClB,CAAC,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;oBACnB,CAAC,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;iBACpB,CAAC;gBACF,uBACE,IAAI,EACJ,SAAS,EAAE,OAAO,EAClB,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,EAAE,EAClB,CAAC,EAAE,IAAI,GAAG,WAAW,GAAG,OAAO,GAAG,UAAU,EAC5C,CAAC,EAAE,GAAG,GAAG,UAAU,GAAG,OAAO,GAAG,SAAS,EACzC,KAAK;oBACL,MAAM,EACN,MAAM,EAAE,CAAC,EACT,WAAW,EAAE,CAAC,EACd,YAAY,EAAE,CAAC,EACf,UAAU,EAAE,CAAC,EACb,aAAa,EAAE,CAAC,EAChB,KAAK,EAAE,IAAI,EACX,KAAK,EAAE,IAAA,cAAO,EAAC,YAAY,EAAE,KAAK,CAAC,EACnC,IAAI,EAAE,IAAA,cAAO,EAAC,WAAW,EAAE,IAAI,EAAE,OAAO,CAAC;oBACzC,8CAA8C;oBAC9C,uCAAuC;oBACvC,MAAM,EAAE,KAAK,EACb,MAAM,EAAE,IAAA,cAAO,EAAC,EAAE,EAAE,MAAM,EAAE;wBAC1B,CAAC,EAAE,EAAE;wBACL,CAAC,EAAE,EAAE;qBACN,CAAC,EACF,WAAW,EAAE,IAAA,cAAO,EAAC,EAAE,EAAE,WAAW,EAAE;wBACpC,4CAA4C;wBAC5C,YAAY,EAAE,KAAK;qBACpB,CAAC,IACC,IAAI,EACP;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IACF,OAAO;QACL,QAAQ,EAAE,cAAc;KACzB,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH;;GAEG;AACH,MAAM,OAAO,GAAG,IAAA,0BAAkB,EAAa,CAAC,OAAmB,EAAE,EAAE;IACrE,MAAM,EAAE,MAAM,KAAc,OAAO,EAAhB,IAAI,UAAK,OAAO,EAA7B,UAAmB,CAAU,CAAC;IACpC,MAAM,EACJ,QAAQ,EAAE,CAAC,GAAG,EAAE,EAChB,CAAC,EAAE,CAAC,GAAG,CAAC,EACR,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,KAErB,MAAM,EADL,UAAU,UACX,MAAM,EALJ,sBAKL,CAAS,CAAC;IACX,MAAM,IAAI,GAAG,EAAE,CAAC;IAChB,KAAK,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;QAC5B,KAAK,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;YAC5B,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;SACvB;KACF;IACD,uCACK,IAAI,KACP,IAAI,EACJ,MAAM,kCAAO,UAAU,KAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,KACzC,KAAK,kCACA,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC,GACxD,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC,KAE7D;AACJ,CAAC,CAAC,CAAC;AAEH,SAAS,YAAY,CAAC,MAAM;IAC1B,IAAI,OAAO,MAAM,KAAK,UAAU;QAAE,OAAO,MAAM,CAAC;IAChD,IAAI,MAAM,KAAK,IAAI;QAAE,OAAO,GAAG,EAAE,CAAC,IAAI,CAAC;IACvC,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;QACrB,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG,KAAK,CAAC;QAC5C,yCAAyC;QACzC,IAAI,QAAQ,KAAK,eAAe,GAAG,CAAC;YAAE,OAAO,IAAA,4BAAgB,EAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC9E,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,YAAY,CAAC,MAAM;IAC1B,IAAI,OAAO,MAAM,KAAK,UAAU;QAAE,OAAO,MAAM,CAAC;IAChD,IAAI,MAAM,KAAK,IAAI;QAAE,OAAO,GAAG,EAAE,CAAC,IAAI,CAAC;IACvC,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;QACrB,MAAM,EAAE,WAAW,EAAE,GAAG,KAAK,CAAC;QAC9B,uCAAuC;QACvC,IAAI,WAAW,KAAK,CAAC;YAAE,OAAO,IAAA,4BAAgB,EAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC/D,CAAC,CAAC;AACJ,CAAC;AAED;;;GAGG;AACI,MAAM,YAAY,GAAgC,GAAG,EAAE;IAC5D,OAAO,CAAC,OAAO,EAAE,EAAE;QACjB,MAAM,UAAU,GAAG,qBAAS,CAAC,EAAE,CAAa,OAAO,CAAC;aACjD,IAAI,CAAC,kBAAM,CAAC;aACZ,IAAI,CAAC,sBAAU,CAAC;aAChB,IAAI,CAAC,WAAW,CAAC;aACjB,IAAI,CAAC,OAAO,CAAC;aACb,IAAI,CAAC,wBAAY,CAAC;aAClB,IAAI,CAAC,oBAAQ,CAAC;aACd,IAAI,CAAC,QAAQ,CAAC;aACd,KAAK,EAAE,CAAC;QACX,OAAO,CAAC,UAAU,CAAC,CAAC;IACtB,CAAC,CAAC;AACJ,CAAC,CAAC;AAbW,QAAA,YAAY,gBAavB"}