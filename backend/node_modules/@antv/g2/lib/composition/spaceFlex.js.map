{"version": 3, "file": "spaceFlex.js", "sourceRoot": "", "sources": ["../../src/composition/spaceFlex.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA,mCAAoC;AAIpC;;GAEG;AACI,MAAM,SAAS,GAAyB,GAAG,EAAE;IAClD,OAAO,CAAC,OAAO,EAAE,EAAE;QACjB,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;QAC7B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC;YAAE,OAAO,EAAE,CAAC;QAExC,MAAM,EACJ,SAAS,GAAG,KAAK,EACjB,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAC7B,OAAO,GAAG,CAAC,EACX,IAAI,EAAE,QAAQ,GACf,GAAG,OAAO,CAAC;QACZ,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC,GAChD,SAAS,KAAK,KAAK;YACj<PERSON>,CAAC,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,CAAC;YAC/B,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;QAEpC,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;QAC1D,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,OAAO,GAAG,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACtE,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,SAAS,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC;QAE9D,MAAM,WAAW,GAAG,EAAE,CAAC;QACvB,IAAI,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YACxC,MAAM,KAAoB,QAAQ,CAAC,CAAC,CAAC,EAA/B,EAAE,IAAI,OAAyB,EAApB,IAAI,cAAf,QAAiB,CAAc,CAAC;YACtC,MAAM,OAAO,GAAG,IAAA,iBAAS,EAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC1C,WAAW,CAAC,IAAI,iBACd,CAAC,SAAS,CAAC,EAAE,IAAI,EACjB,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EACpB,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,EACtC,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC,EAC/B,IAAI,EAAE,OAAO,IACV,IAAI,EACP,CAAC;YACH,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;SAC5B;QACD,OAAO,WAAW,CAAC;IACrB,CAAC,CAAC;AACJ,CAAC,CAAC;AArCW,QAAA,SAAS,aAqCpB;AAEF,iBAAS,CAAC,KAAK,GAAG,EAAE,CAAC"}