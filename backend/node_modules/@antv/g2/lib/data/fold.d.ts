import { DataComponent as DC } from '../runtime';
import { FoldTransform } from '../spec';
export declare function isEmpty(obj: any): boolean;
export type FoldOptions = Omit<FoldTransform, 'type'>;
/**
 * Collapses (or “folds”) one or more data fields into two
 * properties: `key` (contains the original data field name)
 * and `value` (contains the original data value.)
 */
export declare const Fold: DC<FoldOptions>;
