import { DataComponent as DC } from '../runtime';
import { KDEDataTransform } from '../spec';
export type KDEOptions = Omit<KDEDataTransform, 'type'>;
export declare function defined(d: any): boolean;
/**
 * Kernel Density Estimation base on [pdfast](https://www.npmjs.com/package/pdfast),
 * generating probability density function (pdf) using triangular kernel,
 * optimized to run in O(N + K).
 */
export declare const KDE: DC<KDEOptions>;
