import { justify } from './align';
declare function defaultId(d: any): any;
declare function defaultNodes(graph: any): any;
declare function defaultLinks(graph: any): any;
export declare function Sankey(): {
    (arg: any): {
        nodes: any;
        links: any;
    };
    update(graph: any): any;
    nodeId(_: any): typeof defaultId;
    nodeAlign(_: any): typeof justify;
    nodeDepth(_: any): any;
    nodeSort(_: any): any;
    nodeWidth(_: any): number | any;
    nodePadding(_: any): number | any;
    nodes(_: any): typeof defaultNodes;
    links(_: any): typeof defaultLinks;
    linkSort(_: any): any;
    size(_: any): number[] | any;
    extent(_: any): number[][] | any;
    iterations(_: any): number | any;
};
export {};
