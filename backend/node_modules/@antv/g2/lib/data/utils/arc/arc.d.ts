import { ArcData, ArcOptions } from './types';
/**
 * Layout for Arc / Chord diagram with d3 style.
 */
export declare function Arc(options?: ArcOptions): (data: ArcData) => {
    nodes: {
        [x: string]: any;
        id: string;
        x: number | number[];
        y: number | number[];
        value: number;
        frequency: number;
        weight?: number;
        width?: number;
        height?: number;
    }[];
    edges: {
        [x: string]: any;
        source: string;
        target: string;
        sourceWeight: number;
        targetWeight: number;
        x: number | number[];
        y: number | number[];
        value: number;
        weight?: number;
        width?: number;
        height?: number;
        frequency?: number;
    }[];
};
