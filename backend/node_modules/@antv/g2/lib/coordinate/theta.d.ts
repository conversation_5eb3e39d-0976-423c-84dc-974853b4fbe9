import { CoordinateComponent as CC } from '../runtime';
import { ThetaCoordinate } from '../spec';
export type ThetaOptions = ThetaCoordinate;
export declare const getThetaOptions: (options?: ThetaOptions) => {
    type?: "theta";
    startAngle: number;
    endAngle: number;
    innerRadius: number;
    outerRadius: number;
    transform?: import("../spec").CoordinateTransform[];
};
/**
 * Theta = Transpose + Polar.
 */
export declare const Theta: CC<ThetaOptions>;
