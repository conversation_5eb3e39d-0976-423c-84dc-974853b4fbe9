export declare const commonProps: {
    readonly encode: {
        readonly type: "object";
    };
    readonly scale: {
        readonly type: "object";
    };
    readonly data: {
        readonly type: "value";
    };
    readonly transform: {
        readonly type: "array";
    };
    readonly style: {
        readonly type: "object";
    };
    readonly animate: {
        readonly type: "object";
    };
    readonly coordinate: {
        readonly type: "object";
    };
    readonly interaction: {
        readonly type: "object";
    };
    readonly label: {
        readonly type: "array";
        readonly key: "labels";
    };
    readonly axis: {
        readonly type: "object";
    };
    readonly legend: {
        readonly type: "object";
    };
    readonly slider: {
        readonly type: "object";
    };
    readonly scrollbar: {
        readonly type: "object";
    };
    readonly state: {
        readonly type: "object";
    };
    readonly layout: {
        readonly type: "object";
    };
    readonly theme: {
        readonly type: "object";
    };
    readonly title: {
        readonly type: "value";
    };
};
export declare const markProps: {
    readonly tooltip: {
        readonly type: "mix";
    };
    readonly viewStyle: {
        readonly type: "object";
    };
    readonly encode: {
        readonly type: "object";
    };
    readonly scale: {
        readonly type: "object";
    };
    readonly data: {
        readonly type: "value";
    };
    readonly transform: {
        readonly type: "array";
    };
    readonly style: {
        readonly type: "object";
    };
    readonly animate: {
        readonly type: "object";
    };
    readonly coordinate: {
        readonly type: "object";
    };
    readonly interaction: {
        readonly type: "object";
    };
    readonly label: {
        readonly type: "array";
        readonly key: "labels";
    };
    readonly axis: {
        readonly type: "object";
    };
    readonly legend: {
        readonly type: "object";
    };
    readonly slider: {
        readonly type: "object";
    };
    readonly scrollbar: {
        readonly type: "object";
    };
    readonly state: {
        readonly type: "object";
    };
    readonly layout: {
        readonly type: "object";
    };
    readonly theme: {
        readonly type: "object";
    };
    readonly title: {
        readonly type: "value";
    };
};
export declare const compositionProps: {
    readonly labelTransform: {
        readonly type: "array";
    };
    readonly encode: {
        readonly type: "object";
    };
    readonly scale: {
        readonly type: "object";
    };
    readonly data: {
        readonly type: "value";
    };
    readonly transform: {
        readonly type: "array";
    };
    readonly style: {
        readonly type: "object";
    };
    readonly animate: {
        readonly type: "object";
    };
    readonly coordinate: {
        readonly type: "object";
    };
    readonly interaction: {
        readonly type: "object";
    };
    readonly label: {
        readonly type: "array";
        readonly key: "labels";
    };
    readonly axis: {
        readonly type: "object";
    };
    readonly legend: {
        readonly type: "object";
    };
    readonly slider: {
        readonly type: "object";
    };
    readonly scrollbar: {
        readonly type: "object";
    };
    readonly state: {
        readonly type: "object";
    };
    readonly layout: {
        readonly type: "object";
    };
    readonly theme: {
        readonly type: "object";
    };
    readonly title: {
        readonly type: "value";
    };
};
