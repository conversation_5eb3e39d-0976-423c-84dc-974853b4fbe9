{"name": "ltai-backend", "version": "1.0.0", "description": "LtAI异构资源管理平台后端服务", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon --exec \"ts-node --transpile-only src/index.ts\"", "build": "tsc", "test": "jest"}, "dependencies": {"@ant-design/charts": "^2.4.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.2", "socket.io": "^4.7.2", "winston": "^3.10.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.4", "@types/joi": "^17.2.3", "@types/jsonwebtoken": "^9.0.2", "@types/morgan": "^1.9.4", "@types/multer": "^1.4.13", "@types/node": "^20.5.0", "@types/node-cron": "^3.0.8", "jest": "^29.6.2", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "typescript": "^5.1.6"}, "keywords": ["ai", "machine-learning", "resource-management", "training"], "author": "LtAI Team", "license": "MIT"}