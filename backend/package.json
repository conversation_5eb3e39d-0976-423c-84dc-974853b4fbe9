{"name": "ltai-backend", "version": "1.0.0", "description": "LtAI异构资源管理平台后端服务", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.2", "node-cron": "^3.0.2", "joi": "^17.9.2", "winston": "^3.10.0"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/morgan": "^1.9.4", "@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.2", "@types/multer": "^1.4.7", "@types/node": "^20.5.0", "@types/node-cron": "^3.0.8", "@types/joi": "^17.2.3", "typescript": "^5.1.6", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "jest": "^29.6.2", "@types/jest": "^29.5.4"}, "keywords": ["ai", "machine-learning", "resource-management", "training"], "author": "LtAI Team", "license": "MIT"}