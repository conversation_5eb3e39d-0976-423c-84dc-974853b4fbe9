#!/bin/bash

# LtAI 异构资源管理平台启动脚本

echo "🚀 启动 LtAI 异构资源管理平台..."

# 检查 Node.js 版本
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js 16.0.0 或更高版本"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "❌ Node.js 版本过低，请升级到 16.0.0 或更高版本"
    exit 1
fi

# 检查 MongoDB 是否运行
if ! pgrep -x "mongod" > /dev/null; then
    echo "⚠️  MongoDB 未运行，请先启动 MongoDB 服务"
    echo "   Ubuntu/Debian: sudo systemctl start mongod"
    echo "   macOS: brew services start mongodb-community"
    echo "   或手动启动: mongod"
    exit 1
fi

# 安装依赖
echo "📦 安装依赖..."

# 安装后端依赖
if [ ! -d "backend/node_modules" ]; then
    echo "安装后端依赖..."
    cd backend
    npm install
    cd ..
fi

# 安装前端依赖
if [ ! -d "frontend/node_modules" ]; then
    echo "安装前端依赖..."
    cd frontend
    npm install
    cd ..
fi

# 检查环境变量文件
if [ ! -f "backend/.env" ]; then
    echo "📝 创建后端环境变量文件..."
    cp backend/.env.example backend/.env
    echo "请编辑 backend/.env 文件配置数据库连接等信息"
fi

if [ ! -f "frontend/.env" ]; then
    echo "📝 创建前端环境变量文件..."
    cp frontend/.env.example frontend/.env
fi

# 初始化管理员用户
echo "👤 初始化管理员用户..."
cd backend
npm run build 2>/dev/null || echo "构建后端代码..."
npx ts-node src/scripts/initAdmin.ts
cd ..

# 启动服务
echo "🎯 启动服务..."

# 启动后端服务（后台运行）
echo "启动后端服务..."
cd backend
npm run dev &
BACKEND_PID=$!
cd ..

# 等待后端服务启动
echo "等待后端服务启动..."
sleep 5

# 检查后端服务是否启动成功
if curl -s http://localhost:5000/api/health > /dev/null; then
    echo "✅ 后端服务启动成功"
else
    echo "❌ 后端服务启动失败"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

# 启动前端服务
echo "启动前端服务..."
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

# 等待前端服务启动
echo "等待前端服务启动..."
sleep 10

echo ""
echo "🎉 LtAI 异构资源管理平台启动完成！"
echo ""
echo "📱 访问地址:"
echo "   前端: http://localhost:3000"
echo "   后端API: http://localhost:5000"
echo ""
echo "🔑 默认管理员账号:"
echo "   邮箱: <EMAIL>"
echo "   密码: 123456"
echo ""
echo "📋 进程信息:"
echo "   后端进程ID: $BACKEND_PID"
echo "   前端进程ID: $FRONTEND_PID"
echo ""
echo "⏹️  停止服务:"
echo "   kill $BACKEND_PID $FRONTEND_PID"
echo "   或按 Ctrl+C 停止当前脚本"
echo ""

# 等待用户中断
trap "echo ''; echo '🛑 正在停止服务...'; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; echo '✅ 服务已停止'; exit 0" INT

# 保持脚本运行
wait
