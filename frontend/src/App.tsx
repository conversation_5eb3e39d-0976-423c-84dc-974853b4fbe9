import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Layout } from 'antd';
import MainLayout from './components/Layout/MainLayout';
import Login from './pages/Auth/Login';
import Dashboard from './pages/Dashboard/Dashboard';
import Environments from './pages/Environment/Environments';
import Training from './pages/Training/Training';
import Storage from './pages/Storage/Storage';
import Models from './pages/Model/Models';
import Services from './pages/Service/Services';
import Images from './pages/Image/Images';
import Monitor from './pages/Monitor/Monitor';
import DataAnnotation from './pages/DataAnnotation/DataAnnotation';
import { useAuthStore } from './store/authStore';

const App: React.FC = () => {
  const { isAuthenticated } = useAuthStore();

  if (!isAuthenticated) {
    return (
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="*" element={<Navigate to="/login" replace />} />
      </Routes>
    );
  }

  return (
    <MainLayout>
      <Routes>
        <Route path="/" element={<Navigate to="/dashboard" replace />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/environments" element={<Environments />} />
        <Route path="/training" element={<Training />} />
        <Route path="/storage" element={<Storage />} />
        <Route path="/models" element={<Models />} />
        <Route path="/services" element={<Services />} />
        <Route path="/images" element={<Images />} />
        <Route path="/monitor" element={<Monitor />} />
        <Route path="/annotation" element={<DataAnnotation />} />
        <Route path="*" element={<Navigate to="/dashboard" replace />} />
      </Routes>
    </MainLayout>
  );
};

export default App;
