// 用户相关类型
export interface User {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'user' | 'manager';
  tenant: string;
  createdAt: string;
  updatedAt: string;
}

// 开发环境相关类型
export interface Environment {
  id: string;
  name: string;
  description: string;
  framework: string;
  version: string;
  status: 'running' | 'stopped' | 'creating' | 'error';
  resources: {
    cpu: number;
    memory: number;
    gpu: number;
    storage: number;
  };
  accessMethods: string[];
  createdAt: string;
  updatedAt: string;
  userId: string;
}

// 训练任务相关类型
export interface TrainingJob {
  id: string;
  name: string;
  description: string;
  framework: string;
  algorithm: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'paused';
  priority: 'low' | 'medium' | 'high' | 'urgent';

  config: {
    epochs: number;
    batchSize: number;
    learningRate: number;
    optimizer: string;
    lossFunction: string;
    metrics: string[];
    hyperparameters: Record<string, any>;
  };

  resources: {
    cpu: number;
    memory: number;
    gpu: number;
    nodes: number;
    storage: number;
  };

  dataset: {
    name: string;
    path: string;
    size: number;
    trainSplit: number;
    validationSplit: number;
    testSplit: number;
  };

  model: {
    name: string;
    architecture: string;
    inputShape: number[];
    outputShape: number[];
    parameters: number;
  };

  execution: {
    startTime?: string;
    endTime?: string;
    duration?: number;
    currentEpoch?: number;
    totalEpochs: number;
    progress: number;
    logs: string[];
    metrics: {
      epoch: number;
      trainLoss: number;
      validationLoss: number;
      trainAccuracy?: number;
      validationAccuracy?: number;
      timestamp: string;
    }[];
  };

  distributed?: {
    enabled: boolean;
    strategy: 'data_parallel' | 'model_parallel' | 'pipeline_parallel';
    nodes: number;
    gpusPerNode: number;
    masterNode: string;
    workerNodes: string[];
  };

  checkpoint: {
    enabled: boolean;
    frequency: number;
    path: string;
    keepBest: boolean;
    maxKeep: number;
  };

  earlyStopping?: {
    enabled: boolean;
    monitor: string;
    patience: number;
    minDelta: number;
    mode: 'min' | 'max';
  };

  userId: string;
  tenant: string;
  environmentId?: string;
  createdAt: string;
  updatedAt: string;
}

// 存储相关类型
export interface Storage {
  id: string;
  name: string;
  description: string;
  type: 'personal' | 'shared' | 'public';
  path: string;
  size: number;
  used: number;
  quota: number;

  files: {
    name: string;
    path: string;
    size: number;
    type: string;
    mimeType: string;
    uploadedAt: string;
    lastModified: string;
    checksum: string;
    isDirectory: boolean;
    parentPath: string;
  }[];

  sharing: {
    enabled: boolean;
    permissions: {
      userId: {
        id: string;
        username: string;
        email: string;
      };
      permission: 'read' | 'write' | 'admin';
      grantedAt: string;
      grantedBy: string;
    }[];
    publicAccess: boolean;
    publicToken?: string;
    expiresAt?: string;
  };

  backup: {
    enabled: boolean;
    frequency: 'daily' | 'weekly' | 'monthly';
    retention: number;
    lastBackup?: string;
    backupPath?: string;
  };

  sync: {
    enabled: boolean;
    remoteUrl?: string;
    lastSync?: string;
    syncStatus: 'idle' | 'syncing' | 'error';
    syncError?: string;
  };

  userId: string;
  tenant: string;
  createdAt: string;
  updatedAt: string;
}

// 模型相关类型
export interface Model {
  id: string;
  name: string;
  description: string;
  framework: 'tensorflow' | 'pytorch' | 'keras' | 'mxnet' | 'caffe' | 'onnx';
  version: string;
  type: 'classification' | 'regression' | 'detection' | 'segmentation' | 'nlp' | 'other';

  // 模型文件信息
  files: {
    modelFile: string;
    weightsFile?: string;
    configFile?: string;
    metadataFile?: string;
    size: number;
    checksum: string;
  };

  // 模型架构信息
  architecture: {
    name: string;
    layers: number;
    parameters: number;
    inputShape: number[];
    outputShape: number[];
    inputType: string;
    outputType: string;
  };

  // 训练信息
  training: {
    trainingJobId?: string;
    dataset: string;
    epochs: number;
    batchSize: number;
    learningRate: number;
    optimizer: string;
    lossFunction: string;
    trainedAt: string;
    trainingDuration: number;
  };

  // 性能指标
  metrics: {
    accuracy?: number;
    precision?: number;
    recall?: number;
    f1Score?: number;
    loss?: number;
    customMetrics: Record<string, number>;
  };

  // 版本信息
  versioning: {
    majorVersion: number;
    minorVersion: number;
    patchVersion: number;
    parentModelId?: string;
    changelog: string;
    isLatest: boolean;
  };

  // 部署信息
  deployment: {
    isDeployed: boolean;
    deploymentCount: number;
    lastDeployedAt?: string;
    environments: string[];
  };

  // 标签和分类
  tags: string[];
  category: string;

  // 共享设置
  sharing: {
    isPublic: boolean;
    sharedWith: {
      userId: string;
      permission: 'read' | 'write' | 'admin';
      sharedAt: string;
    }[];
  };

  // 使用统计
  stats: {
    downloadCount: number;
    viewCount: number;
    deploymentCount: number;
    lastAccessedAt?: string;
  };

  userId: string;
  tenant: string;
  createdAt: string;
  updatedAt: string;
}

// 模型服务相关类型
export interface ModelService {
  id: string;
  name: string;
  description: string;
  modelId: {
    id: string;
    name: string;
    version: string;
    framework: string;
    type: string;
  };

  // 服务配置
  config: {
    endpoint: string;
    port: number;
    protocol: 'http' | 'https' | 'grpc';
    version: string;
    maxConcurrency: number;
    timeout: number;
    retryAttempts: number;
  };

  // 部署配置
  deployment: {
    environment: 'development' | 'staging' | 'production';
    replicas: number;
    resources: {
      cpu: string;
      memory: string;
      gpu?: string;
    };
    autoScaling: {
      enabled: boolean;
      minReplicas: number;
      maxReplicas: number;
      targetCPUUtilization: number;
      targetMemoryUtilization: number;
    };
  };

  // 服务状态
  status: {
    state: 'pending' | 'deploying' | 'running' | 'stopped' | 'error' | 'updating';
    health: 'healthy' | 'unhealthy' | 'unknown';
    lastHealthCheck: string;
    uptime: number;
    startedAt?: string;
    stoppedAt?: string;
    errorMessage?: string;
  };

  // API配置
  api: {
    inputSchema: Record<string, any>;
    outputSchema: Record<string, any>;
    examples: {
      input: Record<string, any>;
      output: Record<string, any>;
      description: string;
    }[];
    authentication: {
      required: boolean;
      type: 'apikey' | 'jwt' | 'oauth' | 'none';
      config: Record<string, any>;
    };
  };

  // 监控配置
  monitoring: {
    enabled: boolean;
    metrics: {
      requestCount: number;
      errorCount: number;
      averageLatency: number;
      p95Latency: number;
      p99Latency: number;
      throughput: number;
    };
    alerts: {
      enabled: boolean;
      thresholds: {
        errorRate: number;
        latency: number;
        throughput: number;
      };
      channels: string[];
    };
  };

  // 版本控制
  versioning: {
    currentVersion: string;
    previousVersions: {
      version: string;
      deployedAt: string;
      rollbackAvailable: boolean;
    }[];
    rollbackEnabled: boolean;
  };

  // 访问控制
  access: {
    public: boolean;
    allowedUsers: string[];
    allowedIPs: string[];
    rateLimit: {
      enabled: boolean;
      requestsPerMinute: number;
      requestsPerHour: number;
      requestsPerDay: number;
    };
  };

  // 使用统计
  stats: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    lastRequestAt?: string;
    dailyStats: {
      date: string;
      requests: number;
      errors: number;
      avgLatency: number;
    }[];
  };

  userId: string;
  tenant: string;
  createdAt: string;
  updatedAt: string;
}

// 镜像相关类型
export interface Image {
  id: string;
  name: string;
  tag: string;
  description: string;
  size: number;
  framework: string;
  shared: boolean;
  sharedWith: string[];
  createdAt: string;
  updatedAt: string;
  userId: string;
}

// 资源监控相关类型
export interface ResourceMetrics {
  timestamp: string;
  cpu: {
    usage: number;
    total: number;
  };
  memory: {
    usage: number;
    total: number;
  };
  gpu: {
    usage: number;
    total: number;
    temperature: number;
    fanSpeed: number;
  }[];
  storage: {
    usage: number;
    total: number;
  };
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message: string;
  code: number;
}

// 分页类型
export interface PaginationParams {
  page: number;
  pageSize: number;
  total?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}
