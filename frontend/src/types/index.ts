// 用户相关类型
export interface User {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'user' | 'manager';
  tenant: string;
  createdAt: string;
  updatedAt: string;
}

// 开发环境相关类型
export interface Environment {
  id: string;
  name: string;
  description: string;
  framework: string;
  version: string;
  status: 'running' | 'stopped' | 'creating' | 'error';
  resources: {
    cpu: number;
    memory: number;
    gpu: number;
    storage: number;
  };
  accessMethods: string[];
  createdAt: string;
  updatedAt: string;
  userId: string;
}

// 训练任务相关类型
export interface TrainingJob {
  id: string;
  name: string;
  description: string;
  framework: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  resources: {
    cpu: number;
    memory: number;
    gpu: number;
    nodes: number;
  };
  parameters: Record<string, any>;
  logs: string[];
  createdAt: string;
  updatedAt: string;
  userId: string;
}

// 存储相关类型
export interface Storage {
  id: string;
  name: string;
  size: number;
  used: number;
  path: string;
  shared: boolean;
  sharedWith: string[];
  createdAt: string;
  updatedAt: string;
  userId: string;
}

// 模型相关类型
export interface Model {
  id: string;
  name: string;
  description: string;
  framework: string;
  version: string;
  size: number;
  accuracy?: number;
  metrics: Record<string, any>;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  userId: string;
}

// 模型服务相关类型
export interface ModelService {
  id: string;
  name: string;
  modelId: string;
  status: 'running' | 'stopped' | 'deploying' | 'error';
  instances: number;
  endpoint: string;
  apiKey: string;
  resources: {
    cpu: number;
    memory: number;
    gpu: number;
  };
  createdAt: string;
  updatedAt: string;
  userId: string;
}

// 镜像相关类型
export interface Image {
  id: string;
  name: string;
  tag: string;
  description: string;
  size: number;
  framework: string;
  shared: boolean;
  sharedWith: string[];
  createdAt: string;
  updatedAt: string;
  userId: string;
}

// 资源监控相关类型
export interface ResourceMetrics {
  timestamp: string;
  cpu: {
    usage: number;
    total: number;
  };
  memory: {
    usage: number;
    total: number;
  };
  gpu: {
    usage: number;
    total: number;
    temperature: number;
    fanSpeed: number;
  }[];
  storage: {
    usage: number;
    total: number;
  };
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message: string;
  code: number;
}

// 分页类型
export interface PaginationParams {
  page: number;
  pageSize: number;
  total?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}
